#!/bin/bash

echo "🚀 === TSDF点云密度优化测试 ==="
echo ""

# 设置工作目录和环境
cd /root/autodl-tmp/rtab_ws
source devel/setup.bash

echo "📋 密度优化参数测试:"
echo "==================="
echo "✅ 体素大小: 0.035m → 0.025m (更精细)"
echo "✅ 截断距离: 0.04m → 0.05m (更宽容)"
echo "✅ 权重阈值: 0.1 → 0.005 (更包容)"
echo "✅ 表面阈值: 0.5 → 0.8 (更宽松)"
echo "✅ 统计滤波: 50邻居→30邻居, 1.0比率→1.5比率"
echo "✅ 密度增强: 启用"
echo ""

# 检查当前点云数据
echo "🔍 检查当前TSDF点云数据..."
timeout 10s rostopic echo /tsdf_fusion_node/tsdf_pointcloud --noarr | head -10

echo ""
echo "📊 检查点云发布频率..."
timeout 10s rostopic hz /tsdf_fusion_node/tsdf_pointcloud

echo ""
echo "🔲 检查体素标记数据..."
timeout 10s rostopic echo /tsdf_fusion_node/tsdf_voxel_markers --noarr | head -5

echo ""
echo "📈 系统状态检查完成"
echo "请在RViz中查看以下话题的密度改善效果:"
echo "- /tsdf_fusion_node/tsdf_pointcloud (主要点云)"
echo "- /tsdf_fusion_node/tsdf_voxel_markers (体素可视化)"
echo "- /tsdf_fusion_node/tsdf_voxel_grid (体素网格)"

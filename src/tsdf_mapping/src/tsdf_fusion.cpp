#include "tsdf_mapping/tsdf_fusion.h"
#include <pcl/io/pcd_io.h>
#include <pcl/filters/statistical_outlier_removal.h>
#include <cmath>
#include <mutex>  // 用于线程安全
#include <chrono>  // 用于性能监控
#include <thread>  // 用于分批处理

namespace tsdf_mapping {

TSDFFusion::TSDFFusion(ros::NodeHandle& nh)
    : nh_(nh), camera_info_received_(false), use_rtabmap_pose_(false),
      enable_rtabmap_collaboration_(false), coordinate_mismatch_count_(0),
      last_rtabmap_odom_time_(0), enable_gpu_acceleration_(false), gpu_initialized_(false),
      use_voxblox_storage_(true),  // 默认启用VoxBlox存储
      volume_origin_(Eigen::Vector3f::Zero()), volume_origin_initialized_(false),
      volume_update_threshold_(1.0f), migration_distance_threshold_(100.0f),
      last_robot_position_(Eigen::Vector3f::Zero()) {

    // 初始化TF
    tf_buffer_ = std::make_unique<tf2_ros::Buffer>();
    tf_listener_ = std::make_unique<tf2_ros::TransformListener>(*tf_buffer_);

    // 初始化发布器
    mesh_pub_ = nh_.advertise<sensor_msgs::PointCloud2>("tsdf_mesh", 1);
    pointcloud_pub_ = nh_.advertise<sensor_msgs::PointCloud2>("tsdf_pointcloud", 1);
    marker_pub_ = nh_.advertise<visualization_msgs::MarkerArray>("tsdf_markers", 1);
    voxel_markers_pub_ = nh_.advertise<visualization_msgs::MarkerArray>("tsdf_voxel_markers", 1);  // 🆕 体素标记
    voxel_grid_pub_ = nh_.advertise<sensor_msgs::PointCloud2>("tsdf_voxel_grid", 1);  // 🆕 体素网格

    // 🚀 GPU加速：检查是否启用GPU加速
    nh_.param<bool>("enable_gpu_acceleration", enable_gpu_acceleration_, true);  // 默认启用

    if (enable_gpu_acceleration_) {
        ROS_INFO("🚀 GPU加速TSDF：已启用，保持算法级融合架构");
        gpu_tsdf_ = std::make_unique<TSDFCuda>();
    } else {
        ROS_INFO("🔄 CPU模式TSDF：GPU加速已禁用");
    }

    ROS_INFO("TSDF融合节点已初始化");
}

TSDFFusion::~TSDFFusion() {
    // 🚀 GPU加速：清理GPU资源
    if (enable_gpu_acceleration_ && gpu_tsdf_) {
        cleanupGPUResources();
        ROS_INFO("🚀 GPU资源已清理");
    }

    ROS_INFO("TSDF融合节点已销毁");
}

bool TSDFFusion::initialize() {
    // 读取参数 - 优化配置以消除垂直层叠
    nh_.param<float>("voxel_size", voxel_size_, 0.035f);  // 3.5cm体素，平衡精度与噪声抑制
    nh_.param<float>("truncation_distance", truncation_distance_, 0.04f);  // 4cm截断，消除垂直层叠
    nh_.param<float>("max_weight", max_weight_, 100.0f);  // 增加最大权重，提高融合稳定性

    // 🚀 初始化VoxBlox适配器
    nh_.param<bool>("use_voxblox_storage", use_voxblox_storage_, true);
    if (use_voxblox_storage_) {
        float block_size = voxel_size_ * 16;  // 块大小为16个体素
        voxblox_adapter_ = std::make_unique<VoxbloxAdapter>(voxel_size_, block_size);
        ROS_INFO("🚀 VoxBlox适配器已初始化: 体素大小=%.3f, 块大小=%.3f", voxel_size_, block_size);
    }
    nh_.param<std::string>("world_frame", world_frame_, "map");
    nh_.param<std::string>("camera_frame", camera_frame_, "zed_camera_center");  // ZED相机中心坐标系，与RTAB-Map一致

    // 质量控制参数
    nh_.param<float>("depth_min", depth_min_, 0.3f);  // 最小深度阈值
    nh_.param<float>("depth_max", depth_max_, 8.0f);  // 最大深度阈值
    nh_.param<bool>("enable_bilateral_filter", enable_bilateral_filter_, true);  // 启用双边滤波
    nh_.param<bool>("enable_statistical_filter", enable_statistical_filter_, true);  // 启用统计滤波
    nh_.param<int>("statistical_filter_neighbors", statistical_filter_neighbors_, 50);  // 统计滤波邻居数
    nh_.param<float>("statistical_filter_std_ratio", statistical_filter_std_ratio_, 1.0f);  // 统计滤波标准差比率

    // RTAB-Map协作参数 - 位姿订阅中心模式
    nh_.param<bool>("use_rtabmap_pose", use_rtabmap_pose_, true);  // 启用位姿订阅中心模式
    nh_.param<bool>("enable_rtabmap_collaboration", enable_rtabmap_collaboration_, true);  // 启用协作建图

    // 坐标变换控制参数 - 启用TSDF内部变换，与位姿订阅中心形成双重变换
    nh_.param<bool>("disable_internal_coordinate_transform", disable_internal_coordinate_transform_, false);

    // TSDF到RTAB-Map坐标系对齐参数
    nh_.param<bool>("enable_tsdf_rtabmap_alignment", enable_tsdf_rtabmap_alignment_, true);

    // 🚀 GPU体素迁移参数
    nh_.param<float>("migration_distance_threshold", migration_distance_threshold_, 100.0f);
    nh_.param<float>("volume_update_threshold", volume_update_threshold_, 0.5f);

    // 🚀 VoxBlox质量优化参数 - 密度增强配置
    nh_.param<float>("voxblox_min_weight_threshold", voxblox_min_weight_threshold_, 0.005f);  // 🚀 降低到0.005
    nh_.param<float>("voxblox_surface_threshold", voxblox_surface_threshold_, 0.8f);  // 🚀 放宽到0.8
    nh_.param<bool>("enable_surface_smoothing", enable_surface_smoothing_, false);
    nh_.param<bool>("enable_density_enhancement", enable_density_enhancement_, true);  // 🚀 启用密度增强

    ROS_INFO("TSDF质量优化参数:");
    ROS_INFO("  体素大小=%.3f, 截断距离=%.3f, 最大权重=%.1f",
             voxel_size_, truncation_distance_, max_weight_);
    ROS_INFO("  深度范围=[%.1f, %.1f], 双边滤波=%s, 统计滤波=%s",
             depth_min_, depth_max_,
             enable_bilateral_filter_ ? "启用" : "禁用",
             enable_statistical_filter_ ? "启用" : "禁用");
    ROS_INFO("坐标系配置: world_frame=%s, camera_frame=%s",
             world_frame_.c_str(), camera_frame_.c_str());
    ROS_INFO("RTAB-Map协作: use_rtabmap_pose=%s, enable_collaboration=%s",
             use_rtabmap_pose_ ? "启用" : "禁用",
             enable_rtabmap_collaboration_ ? "启用" : "禁用");
    ROS_INFO("坐标变换策略: disable_internal_transform=%s (信任位姿订阅中心)",
             disable_internal_coordinate_transform_ ? "启用" : "禁用");
    ROS_INFO("TSDF坐标系对齐: enable_tsdf_rtabmap_alignment=%s",
             enable_tsdf_rtabmap_alignment_ ? "启用" : "禁用");
    ROS_INFO("🚀 GPU体素迁移参数: migration_distance_threshold=%.1f, volume_update_threshold=%.3f",
             migration_distance_threshold_, volume_update_threshold_);



    // 初始化坐标系转换矩阵（仅在需要时）
    if (!disable_internal_coordinate_transform_) {
        initializeCoordinateTransforms();
        ROS_INFO("🔄 TSDF内部坐标变换已启用");
    } else {
        ROS_INFO("🎯 TSDF内部坐标变换已禁用，信任位姿订阅中心变换结果");
    }

    // 初始化TSDF到RTAB-Map坐标系对齐
    if (enable_tsdf_rtabmap_alignment_) {
        initializeTSDFToRTABMapAlignment();
        ROS_INFO("🎯 TSDF到RTAB-Map坐标系对齐已启用");
    } else {
        ROS_INFO("🎯 TSDF到RTAB-Map坐标系对齐已禁用 (坐标系统一修复模式)");
        ROS_INFO("   信任位姿订阅中心提供的标准化坐标变换，避免双重变换冲突");
    }

    // 初始化深度点云变换
    initializeDepthPointcloudTransform();



    // 初始化质量控制组件
    initializeQualityControl();

    // 验证坐标系配置
    validateCoordinateConfiguration();

    // 初始化RTAB-Map协作
    initializeRTABMapCollaboration();

    // 🚀 GPU加速：初始化GPU TSDF（保持算法级融合架构）
    if (enable_gpu_acceleration_) {
        if (initializeGPUAcceleration()) {
            ROS_INFO("🚀 GPU加速TSDF初始化成功，保持算法级融合架构");
        } else {
            ROS_WARN("⚠️ GPU加速初始化失败，回退到CPU模式");
            enable_gpu_acceleration_ = false;
        }
    }

    return true;
}

void TSDFFusion::processRGBD(const sensor_msgs::ImageConstPtr& rgb_msg,
                             const sensor_msgs::ImageConstPtr& depth_msg,
                             const sensor_msgs::CameraInfoConstPtr& camera_info) {

    // 🔧 强制调试：确认processRGBD被调用
    static int process_count = 0;
    process_count++;

    ROS_ERROR("🔧 [DEBUG] TSDF processRGBD #%d: 函数被调用！RGB时间戳=%.3f, 深度时间戳=%.3f",
              process_count, rgb_msg->header.stamp.toSec(), depth_msg->header.stamp.toSec());

    ROS_INFO_THROTTLE(2.0, "🔧 TSDF processRGBD #%d: 开始处理RGB-D数据，保持算法级融合", process_count);

    static int debug_counter = 0;
    debug_counter++;

    try {
        // 转换图像
        cv_bridge::CvImagePtr rgb_ptr = cv_bridge::toCvCopy(rgb_msg, sensor_msgs::image_encodings::BGR8);
        cv_bridge::CvImagePtr depth_ptr = cv_bridge::toCvCopy(depth_msg, sensor_msgs::image_encodings::TYPE_32FC1);

        cv::Mat rgb_image = rgb_ptr->image;
        cv::Mat depth_image = depth_ptr->image;

        // 应用质量控制预处理
        depth_image = preprocessDepthImage(depth_image);

        // 调试：检查深度图像的数值范围
        double min_depth, max_depth;
        cv::minMaxLoc(depth_image, &min_depth, &max_depth);
        if (debug_counter % 30 == 0) { // 每30帧输出一次调试信息
            ROS_INFO("深度图像范围(预处理后): min=%.2f, max=%.2f, 图像尺寸: %dx%d, 编码: %s",
                     min_depth, max_depth, depth_image.cols, depth_image.rows, depth_msg->encoding.c_str());
        }

        // 更新相机内参
        if (!camera_info_received_) {
            camera_intrinsics_ << camera_info->K[0], camera_info->K[1], camera_info->K[2],
                                  camera_info->K[3], camera_info->K[4], camera_info->K[5],
                                  camera_info->K[6], camera_info->K[7], camera_info->K[8];
            camera_info_received_ = true;
            ROS_INFO("相机内参已更新: fx=%.1f, fy=%.1f, cx=%.1f, cy=%.1f",
                     camera_info->K[0], camera_info->K[4], camera_info->K[2], camera_info->K[5]);
        }

        // 增强的智能坐标系转换逻辑
        geometry_msgs::TransformStamped transform;
        bool transform_found = false;
        std::string effective_camera_frame = camera_frame_;

        // 检测深度图像和配置的坐标系类型
        std::string depth_frame_id = depth_msg->header.frame_id;
        CoordinateFrameType depth_frame_type = detectCoordinateFrameType(depth_frame_id);
        CoordinateFrameType camera_frame_type = detectCoordinateFrameType(camera_frame_);

        // 智能坐标系选择：使用深度图像的原始坐标系进行TF查找
        ROS_INFO_THROTTLE(10.0, "坐标系配置:");
        ROS_INFO_THROTTLE(10.0, "  深度图像坐标系: %s (类型: %s)",
                         depth_frame_id.c_str(),
                         (depth_frame_type == CoordinateFrameType::OPTICAL) ? "光学" :
                         (depth_frame_type == CoordinateFrameType::MECHANICAL) ? "机械" : "未知");
        ROS_INFO_THROTTLE(10.0, "  配置的相机坐标系: %s (类型: %s)",
                         camera_frame_.c_str(),
                         (camera_frame_type == CoordinateFrameType::OPTICAL) ? "光学" :
                         (camera_frame_type == CoordinateFrameType::MECHANICAL) ? "机械" : "未知");

        // 使用深度图像的坐标系进行TF查找，确保与深度数据一致
        effective_camera_frame = depth_frame_id;
        ROS_INFO_THROTTLE(10.0, "  实际使用坐标系: %s (与深度数据一致)", effective_camera_frame.c_str());

        // 位姿订阅中心模式 - 实现流程图设计的订阅位姿式融合
        ROS_INFO_THROTTLE(3.0, "🎯 尝试从位姿订阅中心获取位姿，保持算法级融合架构");

        if (use_rtabmap_pose_ && getRTABMapPose(depth_msg->header.stamp, transform)) {
            transform_found = true;
            ROS_INFO_THROTTLE(5.0, "✅ 位姿订阅中心：成功获取已变换的RTAB-Map位姿 [%.3f, %.3f, %.3f]",
                             transform.transform.translation.x,
                             transform.transform.translation.y,
                             transform.transform.translation.z);

            // 验证变换状态：确保位姿订阅中心已完成坐标变换
            if (disable_internal_coordinate_transform_) {
                ROS_DEBUG_THROTTLE(30.0, "🎯 变换状态验证：使用位姿订阅中心预变换位姿，无需重复变换");
            }
        } else {
            ROS_WARN_THROTTLE(3.0, "❌ 位姿订阅中心：无法获取位姿数据，回退到TF查找模式");

            // 优化的TF查找策略 - 直接使用RTAB-Map发布的map->base_link变换
            std::vector<std::string> candidate_frames = {"base_link", "zed_camera_center", effective_camera_frame};

            for (const auto& candidate_frame : candidate_frames) {
                try {
                    // 增加容忍度，确保能获取到RTAB-Map的TF
                    transform = tf_buffer_->lookupTransform(world_frame_, candidate_frame,
                                                           ros::Time(0), ros::Duration(1.0));
                    transform_found = true;
                    ROS_INFO_THROTTLE(10.0, "✅ 成功获取TF变换: %s -> %s",
                                     world_frame_.c_str(), candidate_frame.c_str());
                    if (candidate_frame != effective_camera_frame) {
                        ROS_INFO_THROTTLE(10.0, "使用备选坐标系进行TF查找: %s -> %s",
                                         world_frame_.c_str(), candidate_frame.c_str());
                        effective_camera_frame = candidate_frame;
                    }

                    // 调试输出：显示找到的变换信息
                    ROS_INFO_THROTTLE(30.0, "✅ TF变换成功: %s -> %s",
                             world_frame_.c_str(), candidate_frame.c_str());
                    ROS_INFO_THROTTLE(30.0, "   位置: [%.3f, %.3f, %.3f]",
                             transform.transform.translation.x,
                             transform.transform.translation.y,
                             transform.transform.translation.z);
                    ROS_INFO_THROTTLE(30.0, "   旋转: [%.3f, %.3f, %.3f, %.3f]",
                             transform.transform.rotation.x,
                             transform.transform.rotation.y,
                             transform.transform.rotation.z,
                             transform.transform.rotation.w);
                    break;
                } catch (tf2::TransformException& ex) {
                    ROS_DEBUG("TF查找失败 %s -> %s: %s", world_frame_.c_str(), candidate_frame.c_str(), ex.what());
                    continue;
                }
            }
        }

        if (!transform_found) {
            ROS_WARN_THROTTLE(5.0, "所有候选坐标系的TF查找都失败，跳过此帧");
            return;
        }

        // 坐标系对齐诊断（仅在启用内部变换时进行）
        if (!disable_internal_coordinate_transform_) {
            diagnoseCoordinateAlignment(depth_frame_id, effective_camera_frame, depth_frame_type,
                                       detectCoordinateFrameType(effective_camera_frame), transform);
        } else {
            ROS_DEBUG_THROTTLE(30.0, "🎯 信任位姿订阅中心变换结果，跳过内部坐标系诊断");
        }

        // 转换为Eigen矩阵 - 直接使用位姿订阅中心提供的变换结果
        Eigen::Matrix4f camera_pose = Eigen::Matrix4f::Identity();
        camera_pose(0, 3) = transform.transform.translation.x;
        camera_pose(1, 3) = transform.transform.translation.y;
        camera_pose(2, 3) = transform.transform.translation.z;

        Eigen::Quaternionf q(transform.transform.rotation.w,
                            transform.transform.rotation.x,
                            transform.transform.rotation.y,
                            transform.transform.rotation.z);
        camera_pose.block<3, 3>(0, 0) = q.toRotationMatrix();

        // 验证位姿订阅中心变换结果
        ROS_DEBUG_THROTTLE(30.0, "🎯 使用位姿订阅中心变换结果: 位置[%.3f, %.3f, %.3f]",
                          transform.transform.translation.x,
                          transform.transform.translation.y,
                          transform.transform.translation.z);

        // 🎯 坐标系统一修复：TSDF坐标系对齐校正
        if (enable_tsdf_rtabmap_alignment_) {
            Eigen::Matrix4f original_pose = camera_pose;
            camera_pose = tsdf_to_rtabmap_alignment_ * camera_pose;

            ROS_INFO_THROTTLE(30.0, "🎯 TSDF坐标系对齐校正 (双重变换模式):");
            ROS_INFO_THROTTLE(30.0, "   原始位置: [%.3f, %.3f, %.3f]",
                              original_pose(0,3), original_pose(1,3), original_pose(2,3));
            ROS_INFO_THROTTLE(30.0, "   校正位置: [%.3f, %.3f, %.3f]",
                              camera_pose(0,3), camera_pose(1,3), camera_pose(2,3));
        } else {
            ROS_INFO_THROTTLE(30.0, "🎯 TSDF坐标系统一模式: 直接使用位姿订阅中心标准化位姿");
            ROS_INFO_THROTTLE(30.0, "   位置: [%.3f, %.3f, %.3f] (无额外变换)",
                              camera_pose(0,3), camera_pose(1,3), camera_pose(2,3));
        }

        // 🚀 混合GPU-CPU处理策略：GPU体素更新 + CPU点云生成
        if (enable_gpu_acceleration_ && gpu_initialized_) {
            // 🔧 关键调试：监控位姿变化
        static Eigen::Vector3f last_position(0, 0, 0);
        static bool first_pose = true;

        Eigen::Vector3f current_position(camera_pose(0,3), camera_pose(1,3), camera_pose(2,3));

        if (first_pose) {
            last_position = current_position;
            first_pose = false;
            ROS_INFO("🎯 首次位姿记录: [%.3f, %.3f, %.3f]",
                     current_position.x(), current_position.y(), current_position.z());
        } else {
            float movement = (current_position - last_position).norm();
            ROS_INFO_THROTTLE(2.0, "🔍 位姿变化监控: 当前[%.3f, %.3f, %.3f] 移动距离=%.3f米",
                             current_position.x(), current_position.y(), current_position.z(), movement);

            if (movement > 0.01f) {  // 1cm以上的移动
                ROS_INFO("✅ 检测到机器人移动: %.3f米", movement);
                last_position = current_position;
            } else {
                ROS_WARN_THROTTLE(5.0, "⚠️ 机器人位姿几乎静止，移动距离仅%.3f米", movement);
            }
        }

        // 🔧 动态更新体素原点：让体素网格跟随机器人移动
            updateDynamicVolumeOrigin(camera_pose);

            // GPU加速路径：利用RTX 4090的强大计算能力进行体素更新
            ROS_INFO_THROTTLE(5.0, "🚀 使用GPU加速体素更新，CPU负责点云生成（混合策略）");

            // 🔧 增强的GPU处理与CPU降级机制
            ROS_ERROR("🔧 [DEBUG] 开始GPU处理...");
            bool gpu_success = processRGBDWithGPU(rgb_msg, depth_msg, camera_info, camera_pose);
            ROS_ERROR("🔧 [DEBUG] GPU处理结果: %s", gpu_success ? "成功" : "失败");

            if (gpu_success) {
                ROS_ERROR("🔧 [DEBUG] GPU体素更新成功，继续CPU点云生成");
                // 🔄 GPU处理成功后，继续执行CPU处理进行点云生成
                // 不直接返回，让CPU处理生成点云
            } else {
                ROS_ERROR("🔧 [DEBUG] GPU处理失败，检查是否需要永久切换到CPU模式");

                // 🔧 检查GPU连续失败次数，决定是否永久切换到CPU模式
                static int gpu_failure_count = 0;
                static ros::Time last_gpu_failure_time = ros::Time::now();

                gpu_failure_count++;
                ros::Time current_time = ros::Time::now();

                // 如果在5分钟内连续失败超过10次，永久切换到CPU模式
                if (gpu_failure_count >= 10 &&
                    (current_time - last_gpu_failure_time).toSec() < 300.0) {

                    ROS_ERROR("❌ GPU连续失败%d次，永久切换到CPU模式", gpu_failure_count);
                    enable_gpu_acceleration_ = false;

                    // 清理GPU资源
                    cleanupGPUResources();

                    ROS_WARN("🔄 已切换到纯CPU模式，性能将降低但更稳定");
                } else if ((current_time - last_gpu_failure_time).toSec() > 300.0) {
                    // 重置失败计数器（5分钟后）
                    gpu_failure_count = 1;
                }

                last_gpu_failure_time = current_time;

                // 继续执行CPU处理作为备选
                ROS_INFO_THROTTLE(5.0, "🔄 使用CPU处理作为备选方案");
            }
        }

        // CPU处理路径：轻量化优化版本
        ROS_INFO_THROTTLE(5.0, "🔄 使用CPU处理RGB-D数据（轻量化版本）");

        // 🔧 关键修复：CPU模式下也需要动态更新体素原点
        updateDynamicVolumeOrigin(camera_pose);
        ROS_INFO_THROTTLE(3.0, "🔧 CPU模式：动态体素原点已更新");

        // 处理每个像素 - 轻量化采样策略
        int step = 3;  // 🚀 轻量化：增加步长以减少计算量

        // 统计有效深度点数量和深度范围
        int valid_points = 0;
        float min_valid_depth = std::numeric_limits<float>::max();
        float max_valid_depth = 0.0f;

        for (int v = 0; v < depth_image.rows; v += step) {
            for (int u = 0; u < depth_image.cols; u += step) {
                float depth = depth_image.at<float>(v, u);

                // 检查深度值有效性
                if (!isValidDepth(depth)) {
                    continue;
                }

                valid_points++;
                min_valid_depth = std::min(min_valid_depth, depth);
                max_valid_depth = std::max(max_valid_depth, depth);

                // 🎯 算法级融合修复：计算3D点时直接使用RTAB-Map兼容的坐标系
                // 光学坐标系 -> 机械坐标系转换，确保与RTAB-Map完全一致
                float x_optical = (u - camera_info->K[2]) * depth / camera_info->K[0];  // X右
                float y_optical = (v - camera_info->K[5]) * depth / camera_info->K[4];  // Y下
                float z_optical = depth;                                                // Z前

                // 转换为RTAB-Map使用的机械坐标系 (X前, Y左, Z上)
                float x = z_optical;   // X_mechanical = Z_optical (前方向)
                float y = -x_optical;  // Y_mechanical = -X_optical (左方向)
                float z = -y_optical;  // Z_mechanical = -Y_optical (上方向)

                // 使用机械坐标系的点直接转换为世界坐标
                Eigen::Vector3f camera_point(x, y, z);
                Eigen::Vector3f world_point = (camera_pose * camera_point.homogeneous()).head<3>();

                // 获取颜色
                cv::Vec3b color = rgb_image.at<cv::Vec3b>(v, u);

                // 计算准确的表面法向量
                Eigen::Vector3f surface_normal = calculateSurfaceNormal(u, v, depth, camera_info);

                // 分析表面曲率
                float curvature = analyzeSurfaceCurvature(surface_normal, world_point);
                int curvature_level = classifyCurvatureLevel(curvature);

                // 根据曲率级别选择更新策略
                switch (curvature_level) {
                    case 2:  // 高曲率 - 单尺度精细更新
                        updateSingleScaleVoxels(world_point, depth, color, camera_pose, surface_normal);
                        break;
                    case 1:  // 中曲率 - 双尺度更新
                        updateDualScaleVoxels(world_point, depth, color, camera_pose, surface_normal);
                        break;
                    case 0:  // 低曲率 - 三尺度更新
                    default:
                        updateTripleScaleVoxels(world_point, depth, color, camera_pose, surface_normal);
                        break;
                }
            }
        }

        // 增强的调试信息：包含曲率分析统计和坐标系修复状态
        if (debug_counter % 30 == 0 && valid_points > 0) {
            ROS_INFO("🎯 TSDF算法级融合处理统计: 有效点数=%d, 深度范围=[%.2f, %.2f]m",
                     valid_points, min_valid_depth, max_valid_depth);
            ROS_INFO("✅ 坐标系修复: 光学坐标系->机械坐标系转换已应用，与RTAB-Map完全兼容");
            ROS_INFO("多尺度更新策略已启用，基于曲率分析的自适应体素更新");
        }

    } catch (cv_bridge::Exception& e) {
        ROS_ERROR("cv_bridge异常: %s", e.what());
        return;
    } catch (std::exception& e) {
        ROS_ERROR("处理RGBD数据时发生异常: %s", e.what());
        return;
    }
}

CoordinateFrameType TSDFFusion::detectCoordinateFrameType(const std::string& frame_id) const {
    // 增强的坐标系类型检测 - 支持ZED相机坐标系
    ROS_DEBUG("检测坐标系类型: %s", frame_id.c_str());

    // 光学坐标系识别规则（更全面）
    if (frame_id.find("optical") != std::string::npos ||
        frame_id.find("camera_link") != std::string::npos ||
        frame_id.find("camera_optical") != std::string::npos ||
        frame_id.find("_optical_frame") != std::string::npos ||
        frame_id.find("color_optical_frame") != std::string::npos ||
        frame_id.find("depth_optical_frame") != std::string::npos ||
        frame_id.find("left_camera_optical") != std::string::npos ||
        frame_id.find("right_camera_optical") != std::string::npos ||
        frame_id.find("zed_left_camera_optical") != std::string::npos ||
        frame_id.find("zed_right_camera_optical") != std::string::npos) {
        ROS_DEBUG("识别为光学坐标系: %s", frame_id.c_str());
        return CoordinateFrameType::OPTICAL;
    }

    // 机械坐标系识别规则（更全面）
    else if (frame_id.find("base_link") != std::string::npos ||
             frame_id.find("robot") != std::string::npos ||
             frame_id.find("base_footprint") != std::string::npos ||
             frame_id.find("chassis") != std::string::npos ||
             frame_id.find("body") != std::string::npos ||
             frame_id.find("platform") != std::string::npos ||
             frame_id.find("stereo_camera") != std::string::npos ||
             frame_id.find("camera_base") != std::string::npos ||
             frame_id.find("zed_camera_center") != std::string::npos) {
        ROS_DEBUG("识别为机械坐标系: %s", frame_id.c_str());
        return CoordinateFrameType::MECHANICAL;
    }

    ROS_WARN("未知坐标系类型: %s，将作为机械坐标系处理", frame_id.c_str());
    return CoordinateFrameType::UNKNOWN;
}

VoxelIndex TSDFFusion::worldToVoxel(const Eigen::Vector3f& world_point) const {
    // 🔧 关键修复：与GPU端保持一致，正确处理动态体素原点偏移
    if (volume_origin_initialized_) {
        VoxelIndex voxel_idx(
            static_cast<int>(std::floor((world_point.x() - volume_origin_.x()) / voxel_size_)),
            static_cast<int>(std::floor((world_point.y() - volume_origin_.y()) / voxel_size_)),
            static_cast<int>(std::floor((world_point.z() - volume_origin_.z()) / voxel_size_))
        );

        // 🔍 调试输出：验证CPU端坐标转换修复
        static int debug_count = 0;
        debug_count++;
        if (debug_count % 5000 == 0) {  // 每5000次输出一次调试信息
            ROS_INFO("🔧 CPU坐标转换修复验证 #%d: 世界[%.3f,%.3f,%.3f] -> 体素[%d,%d,%d], 原点[%.3f,%.3f,%.3f]",
                     debug_count, world_point.x(), world_point.y(), world_point.z(),
                     voxel_idx.x, voxel_idx.y, voxel_idx.z,
                     volume_origin_.x(), volume_origin_.y(), volume_origin_.z());
        }

        return voxel_idx;
    } else {
        // 体素原点未初始化时的回退处理
        return VoxelIndex(
            static_cast<int>(std::floor(world_point.x() / voxel_size_)),
            static_cast<int>(std::floor(world_point.y() / voxel_size_)),
            static_cast<int>(std::floor(world_point.z() / voxel_size_))
        );
    }
}

Eigen::Vector3f TSDFFusion::voxelToWorld(const VoxelIndex& voxel_idx) const {
    // 🔧 关键修复：与GPU端保持一致，正确处理动态体素原点偏移
    if (volume_origin_initialized_) {
        Eigen::Vector3f world_pos(
            volume_origin_.x() + (voxel_idx.x + 0.5f) * voxel_size_,
            volume_origin_.y() + (voxel_idx.y + 0.5f) * voxel_size_,
            volume_origin_.z() + (voxel_idx.z + 0.5f) * voxel_size_
        );

        // 🔍 调试输出：验证CPU端坐标转换修复
        static int debug_count = 0;
        debug_count++;
        if (debug_count % 5000 == 0) {  // 每5000次输出一次调试信息
            ROS_INFO("🔧 CPU坐标转换修复验证 #%d: 体素[%d,%d,%d] -> 世界[%.3f,%.3f,%.3f], 原点[%.3f,%.3f,%.3f]",
                     debug_count, voxel_idx.x, voxel_idx.y, voxel_idx.z,
                     world_pos.x(), world_pos.y(), world_pos.z(),
                     volume_origin_.x(), volume_origin_.y(), volume_origin_.z());
        }

        return world_pos;
    } else {
        // 体素原点未初始化时的回退处理
        return Eigen::Vector3f(
            (voxel_idx.x + 0.5f) * voxel_size_,
            (voxel_idx.y + 0.5f) * voxel_size_,
            (voxel_idx.z + 0.5f) * voxel_size_
        );
    }
}

void TSDFFusion::updateTSDFVoxelsAroundPoint(const Eigen::Vector3f& surface_point,
                                           float depth,
                                           const cv::Vec3b& color,
                                           const Eigen::Matrix4f& camera_pose,
                                           const Eigen::Vector3f& surface_normal) {

    // 相机位置
    Eigen::Vector3f camera_position = camera_pose.block<3, 1>(0, 3);

    // 正确的TSDF算法：更新表面点周围的体素
    // 计算需要更新的体素范围 - 使用0.5x步长效果（更精细的体素更新）
    VoxelIndex center_voxel = worldToVoxel(surface_point);
    int radius = static_cast<int>(std::ceil(truncation_distance_ / voxel_size_ * 0.5f)) + 1;

    // 遍历表面点周围的体素
    for (int dx = -radius; dx <= radius; dx++) {
        for (int dy = -radius; dy <= radius; dy++) {
            for (int dz = -radius; dz <= radius; dz++) {
                VoxelIndex voxel_idx(center_voxel.x + dx, center_voxel.y + dy, center_voxel.z + dz);
                Eigen::Vector3f voxel_center = voxelToWorld(voxel_idx);

                // 计算体素中心到表面点的距离
                float distance_to_surface = (voxel_center - surface_point).norm();

                // 只更新截断距离内的体素
                if (distance_to_surface <= truncation_distance_) {
                    // 计算正确的SDF值：体素到表面的有向距离
                    Eigen::Vector3f to_surface = surface_point - voxel_center;
                    Eigen::Vector3f to_camera = camera_position - voxel_center;

                    // 使用点积判断体素在表面前还是后
                    float sdf = distance_to_surface;
                    if (to_surface.dot(to_camera) < 0) {
                        sdf = -sdf;  // 体素在表面后面
                    }

                    // 截断和归一化SDF
                    sdf = std::max(-truncation_distance_, std::min(truncation_distance_, sdf));
                    sdf /= truncation_distance_;  // 归一化到[-1, 1]

                    // 计算权重（距离表面越近权重越高）
                    float weight = calculateAdaptiveWeight(sdf, depth);
                    updateTSDFVoxel(voxel_idx, sdf, weight, color);
                }
            }
        }
    }
}

void TSDFFusion::updateTSDFVoxel(const VoxelIndex& voxel_idx,
                                float sdf_value,
                                float weight,
                                const cv::Vec3b& color) {

    // 高级质量控制：几何一致性检查
    if (std::abs(sdf_value) > 1.0f) {
        ROS_WARN_THROTTLE(5.0, "检测到异常SDF值: %.3f，跳过更新", sdf_value);
        return;
    }

    // 优化的权重一致性检查：降低阈值以保留更多有效体素
    if (weight < 0.05f) {
        return;  // 跳过过低权重的更新
    }

    auto it = tsdf_volume_.find(voxel_idx);
    if (it == tsdf_volume_.end()) {
        // 创建新体素
        TSDFVoxel voxel;
        voxel.tsdf_value = sdf_value;
        voxel.weight = weight;
        voxel.r = color[2];  // OpenCV使用BGR
        voxel.g = color[1];
        voxel.b = color[0];
        tsdf_volume_[voxel_idx] = voxel;
    } else {
        // 更新现有体素
        TSDFVoxel& voxel = it->second;
        float old_tsdf = voxel.tsdf_value;
        float total_weight = voxel.weight + weight;

        if (total_weight > 0) {
            // 加权平均
            voxel.tsdf_value = (voxel.tsdf_value * voxel.weight + sdf_value * weight) / total_weight;

            // 高级质量控制：检测异常变化
            if (voxel.weight > 5.0f && std::abs(voxel.tsdf_value - old_tsdf) > 0.5f) {
                ROS_WARN_THROTTLE(10.0, "检测到TSDF异常变化: %.3f -> %.3f", old_tsdf, voxel.tsdf_value);
                // 使用更保守的更新策略
                voxel.tsdf_value = old_tsdf * 0.8f + voxel.tsdf_value * 0.2f;
            }

            // 更新颜色
            voxel.r = static_cast<uint8_t>((voxel.r * voxel.weight + color[2] * weight) / total_weight);
            voxel.g = static_cast<uint8_t>((voxel.g * voxel.weight + color[1] * weight) / total_weight);
            voxel.b = static_cast<uint8_t>((voxel.b * voxel.weight + color[0] * weight) / total_weight);

            voxel.weight = std::min(total_weight, max_weight_);
        }
    }
}

// 质量控制相关方法
void TSDFFusion::initializeQualityControl() {
    ROS_INFO("初始化质量控制系统");
    ROS_INFO("  深度范围过滤: [%.1f, %.1f]m", depth_min_, depth_max_);
    ROS_INFO("  双边滤波: %s", enable_bilateral_filter_ ? "启用" : "禁用");
    ROS_INFO("  统计滤波: %s (邻居数=%d, 标准差比率=%.1f)",
             enable_statistical_filter_ ? "启用" : "禁用",
             statistical_filter_neighbors_, statistical_filter_std_ratio_);
}

bool TSDFFusion::isValidDepth(float depth) const {
    return !std::isnan(depth) && !std::isinf(depth) &&
           depth >= depth_min_ && depth <= depth_max_;
}

cv::Mat TSDFFusion::preprocessDepthImage(const cv::Mat& depth_image) const {
    cv::Mat processed = depth_image.clone();

    // 应用双边滤波去除噪声
    if (enable_bilateral_filter_) {
        cv::Mat filtered;
        cv::bilateralFilter(processed, filtered, 5, 50.0, 50.0);
        processed = filtered;
    }

    // 深度值范围过滤
    for (int v = 0; v < processed.rows; ++v) {
        for (int u = 0; u < processed.cols; ++u) {
            float& depth = processed.at<float>(v, u);
            if (!isValidDepth(depth)) {
                depth = 0.0f;  // 设置为无效值
            }
        }
    }

    // 新增：计算深度梯度用于曲率分析
    cv::Mat depth_grad_x, depth_grad_y;
    cv::Sobel(processed, depth_grad_x, CV_32F, 1, 0, 3);
    cv::Sobel(processed, depth_grad_y, CV_32F, 0, 1, 3);

    // 存储梯度信息供后续使用（通过类成员变量）
    depth_gradient_x_ = depth_grad_x.clone();
    depth_gradient_y_ = depth_grad_y.clone();

    // 边缘检测增强
    cv::Mat depth_magnitude;
    cv::magnitude(depth_grad_x, depth_grad_y, depth_magnitude);

    // 存储梯度幅值
    depth_gradient_magnitude_ = depth_magnitude.clone();

    return processed;
}

cv::Mat TSDFFusion::applyBilateralFilter(const cv::Mat& depth_image) const {
    cv::Mat filtered;
    cv::bilateralFilter(depth_image, filtered, 5, 50.0, 50.0);
    return filtered;
}

pcl::PointCloud<pcl::PointXYZRGB>::Ptr TSDFFusion::applyStatisticalFilter(
    const pcl::PointCloud<pcl::PointXYZRGB>::Ptr& cloud) const {

    if (!enable_statistical_filter_ || cloud->points.empty()) {
        return cloud;
    }

    pcl::PointCloud<pcl::PointXYZRGB>::Ptr filtered_cloud(new pcl::PointCloud<pcl::PointXYZRGB>);
    pcl::StatisticalOutlierRemoval<pcl::PointXYZRGB> sor;
    sor.setInputCloud(cloud);
    sor.setMeanK(statistical_filter_neighbors_);
    sor.setStddevMulThresh(statistical_filter_std_ratio_);
    sor.filter(*filtered_cloud);

    return filtered_cloud;
}

float TSDFFusion::calculateAdaptiveWeight(float sdf_value, float depth) const {
    // 基础权重
    float base_weight = 1.0f;

    // 距离表面越近权重越高（优化阈值）
    float surface_weight;
    if (std::abs(sdf_value) < 0.2f) {
        surface_weight = 10.0f;  // 非常接近表面
    } else if (std::abs(sdf_value) < 0.4f) {
        surface_weight = 6.0f;   // 接近表面
    } else {
        surface_weight = 3.0f;   // 远离表面
    }

    // 深度权重：近距离权重更高，远距离适当降低
    float depth_weight = std::max(0.3f, std::min(2.5f, 2.5f / depth));

    // 角度权重：考虑观察角度的影响
    float angle_weight = 1.0f;  // 可以根据表面法向量与视线角度调整

    return base_weight * surface_weight * depth_weight * angle_weight;
}

float TSDFFusion::calculateAdaptiveSurfaceThreshold(const TSDFVoxel& voxel, const VoxelIndex& idx) const {
    // 🚀 调整表面提取阈值：使用更宽松的阈值以生成更多点云
    float base_threshold = 0.5f;  // 从0.3f增加到0.5f，更宽松

    // 权重因子：权重越高，阈值越严格，但整体更宽松
    float weight_factor = 1.0f;
    if (voxel.weight > 5.0f) {
        weight_factor = 1.0f;  // 从0.8f调整到1.0f，不再过度严格
    } else if (voxel.weight < 2.0f) {
        weight_factor = 1.5f;  // 从1.3f增加到1.5f，更宽松
    }

    // 邻域一致性因子：检查周围体素的一致性
    float consistency_factor = 1.0f;
    int consistent_neighbors = 0;
    int total_neighbors = 0;

    // 检查3x3x3邻域
    for (int dx = -1; dx <= 1; dx++) {
        for (int dy = -1; dy <= 1; dy++) {
            for (int dz = -1; dz <= 1; dz++) {
                if (dx == 0 && dy == 0 && dz == 0) continue;

                VoxelIndex neighbor_idx(idx.x + dx, idx.y + dy, idx.z + dz);
                auto it = tsdf_volume_.find(neighbor_idx);
                if (it != tsdf_volume_.end()) {
                    const TSDFVoxel& neighbor = it->second;
                    total_neighbors++;

                    // 检查TSDF值的一致性
                    if (neighbor.weight > 1.0f &&
                        std::abs(neighbor.tsdf_value - voxel.tsdf_value) < 0.4f) {
                        consistent_neighbors++;
                    }
                }
            }
        }
    }

    if (total_neighbors > 0) {
        float consistency_ratio = static_cast<float>(consistent_neighbors) / total_neighbors;
        if (consistency_ratio > 0.6f) {
            consistency_factor = 0.9f;  // 高一致性，使用更严格阈值
        } else if (consistency_ratio < 0.3f) {
            consistency_factor = 1.2f;  // 低一致性，使用更宽松阈值
        }
    }

    return base_threshold * weight_factor * consistency_factor;
}

Eigen::Vector3f TSDFFusion::calculateSurfaceNormal(int u, int v, float depth,
                                                  const sensor_msgs::CameraInfoConstPtr& camera_info) const {
    // 检查梯度数据是否可用
    if (depth_gradient_x_.empty() || depth_gradient_y_.empty()) {
        // 回退到默认法向量
        return Eigen::Vector3f(0, 0, -1);
    }

    // 获取深度梯度
    Eigen::Vector2f grad = estimateDepthGradient(u, v);
    float grad_x = grad.x();
    float grad_y = grad.y();

    // 相机内参
    float fx = camera_info->K[0];
    float fy = camera_info->K[4];

    // 计算3D空间中的梯度
    float dx_3d = grad_x / fx;
    float dy_3d = grad_y / fy;

    // 构造表面法向量（在相机坐标系中）
    Eigen::Vector3f normal(-dx_3d, -dy_3d, 1.0f);

    // 归一化
    normal.normalize();

    return normal;
}

Eigen::Vector2f TSDFFusion::estimateDepthGradient(int u, int v) const {
    // 边界检查
    if (u <= 0 || u >= depth_gradient_x_.cols - 1 ||
        v <= 0 || v >= depth_gradient_x_.rows - 1) {
        return Eigen::Vector2f(0.0f, 0.0f);
    }

    // 获取Sobel梯度
    float grad_x = depth_gradient_x_.at<float>(v, u);
    float grad_y = depth_gradient_y_.at<float>(v, u);

    return Eigen::Vector2f(grad_x, grad_y);
}

float TSDFFusion::analyzeSurfaceCurvature(const Eigen::Vector3f& surface_normal,
                                         const Eigen::Vector3f& world_point) const {
    // 检查梯度幅值数据是否可用
    if (depth_gradient_magnitude_.empty()) {
        return 0.0f;  // 默认低曲率
    }

    // 将世界坐标投影回图像坐标（简化版本）
    // 这里使用梯度幅值作为曲率的近似估计

    // 计算法向量变化率作为曲率指标
    float normal_magnitude = surface_normal.norm();
    if (normal_magnitude < 1e-6) {
        return 0.0f;
    }

    // 使用法向量的Z分量变化作为曲率估计
    // Z分量越偏离-1，表示表面越弯曲
    float curvature = std::abs(surface_normal.z() + 1.0f);

    // 归一化到[0, 1]范围
    curvature = std::min(1.0f, curvature);

    return curvature;
}

int TSDFFusion::classifyCurvatureLevel(float curvature) const {
    // 根据曲率值分类为三个级别
    if (curvature > 0.6f) {
        return 2;  // 高曲率 - 使用单尺度精细更新
    } else if (curvature > 0.3f) {
        return 1;  // 中曲率 - 使用双尺度更新
    } else {
        return 0;  // 低曲率 - 使用三尺度更新
    }
}

void TSDFFusion::updateSingleScaleVoxels(const Eigen::Vector3f& surface_point, float depth,
                                        const cv::Vec3b& color, const Eigen::Matrix4f& camera_pose,
                                        const Eigen::Vector3f& surface_normal) {
    // 高曲率区域：使用精细的单尺度更新
    Eigen::Vector3f camera_position = camera_pose.block<3, 1>(0, 3);
    VoxelIndex center_voxel = worldToVoxel(surface_point);

    // 使用较小的更新半径以保持细节
    int radius = static_cast<int>(std::ceil(truncation_distance_ / voxel_size_ * 0.8f)) + 1;

    for (int dx = -radius; dx <= radius; dx++) {
        for (int dy = -radius; dy <= radius; dy++) {
            for (int dz = -radius; dz <= radius; dz++) {
                VoxelIndex voxel_idx(center_voxel.x + dx, center_voxel.y + dy, center_voxel.z + dz);
                Eigen::Vector3f voxel_center = voxelToWorld(voxel_idx);

                float distance_to_surface = (voxel_center - surface_point).norm();

                if (distance_to_surface <= truncation_distance_) {
                    // 使用准确的表面法向量计算SDF
                    Eigen::Vector3f to_surface = surface_point - voxel_center;
                    float sdf = to_surface.dot(surface_normal);

                    // 截断和归一化
                    sdf = std::max(-truncation_distance_, std::min(truncation_distance_, sdf));
                    sdf /= truncation_distance_;

                    // 高曲率区域使用更高权重
                    float weight = calculateAdaptiveWeight(sdf, depth) * 1.5f;
                    updateTSDFVoxel(voxel_idx, sdf, weight, color);
                }
            }
        }
    }
}

void TSDFFusion::updateDualScaleVoxels(const Eigen::Vector3f& surface_point, float depth,
                                      const cv::Vec3b& color, const Eigen::Matrix4f& camera_pose,
                                      const Eigen::Vector3f& surface_normal) {
    // 中曲率区域：使用双尺度更新策略
    Eigen::Vector3f camera_position = camera_pose.block<3, 1>(0, 3);

    // 精细尺度更新
    VoxelIndex center_voxel = worldToVoxel(surface_point);
    int fine_radius = static_cast<int>(std::ceil(truncation_distance_ / voxel_size_ * 0.6f)) + 1;

    for (int dx = -fine_radius; dx <= fine_radius; dx++) {
        for (int dy = -fine_radius; dy <= fine_radius; dy++) {
            for (int dz = -fine_radius; dz <= fine_radius; dz++) {
                VoxelIndex voxel_idx(center_voxel.x + dx, center_voxel.y + dy, center_voxel.z + dz);
                Eigen::Vector3f voxel_center = voxelToWorld(voxel_idx);

                float distance_to_surface = (voxel_center - surface_point).norm();

                if (distance_to_surface <= truncation_distance_ * 0.7f) {
                    Eigen::Vector3f to_surface = surface_point - voxel_center;
                    float sdf = to_surface.dot(surface_normal);

                    sdf = std::max(-truncation_distance_, std::min(truncation_distance_, sdf));
                    sdf /= truncation_distance_;

                    float weight = calculateAdaptiveWeight(sdf, depth) * 1.2f;
                    updateTSDFVoxel(voxel_idx, sdf, weight, color);
                }
            }
        }
    }

    // 粗糙尺度更新（更大范围，较低权重）
    int coarse_radius = static_cast<int>(std::ceil(truncation_distance_ / voxel_size_ * 1.2f)) + 1;

    for (int dx = -coarse_radius; dx <= coarse_radius; dx += 2) {  // 跳步采样
        for (int dy = -coarse_radius; dy <= coarse_radius; dy += 2) {
            for (int dz = -coarse_radius; dz <= coarse_radius; dz += 2) {
                VoxelIndex voxel_idx(center_voxel.x + dx, center_voxel.y + dy, center_voxel.z + dz);
                Eigen::Vector3f voxel_center = voxelToWorld(voxel_idx);

                float distance_to_surface = (voxel_center - surface_point).norm();

                if (distance_to_surface <= truncation_distance_) {
                    Eigen::Vector3f to_surface = surface_point - voxel_center;
                    float sdf = to_surface.dot(surface_normal);

                    sdf = std::max(-truncation_distance_, std::min(truncation_distance_, sdf));
                    sdf /= truncation_distance_;

                    float weight = calculateAdaptiveWeight(sdf, depth) * 0.8f;
                    updateTSDFVoxel(voxel_idx, sdf, weight, color);
                }
            }
        }
    }
}

void TSDFFusion::updateTripleScaleVoxels(const Eigen::Vector3f& surface_point, float depth,
                                        const cv::Vec3b& color, const Eigen::Matrix4f& camera_pose,
                                        const Eigen::Vector3f& surface_normal) {
    // 低曲率区域：使用三尺度更新策略，优化大平面区域
    Eigen::Vector3f camera_position = camera_pose.block<3, 1>(0, 3);
    VoxelIndex center_voxel = worldToVoxel(surface_point);

    // 第一尺度：精细更新（核心区域）
    int fine_radius = static_cast<int>(std::ceil(truncation_distance_ / voxel_size_ * 0.4f)) + 1;

    for (int dx = -fine_radius; dx <= fine_radius; dx++) {
        for (int dy = -fine_radius; dy <= fine_radius; dy++) {
            for (int dz = -fine_radius; dz <= fine_radius; dz++) {
                VoxelIndex voxel_idx(center_voxel.x + dx, center_voxel.y + dy, center_voxel.z + dz);
                Eigen::Vector3f voxel_center = voxelToWorld(voxel_idx);

                float distance_to_surface = (voxel_center - surface_point).norm();

                if (distance_to_surface <= truncation_distance_ * 0.5f) {
                    Eigen::Vector3f to_surface = surface_point - voxel_center;
                    float sdf = to_surface.dot(surface_normal);

                    sdf = std::max(-truncation_distance_, std::min(truncation_distance_, sdf));
                    sdf /= truncation_distance_;

                    float weight = calculateAdaptiveWeight(sdf, depth) * 1.0f;
                    updateTSDFVoxel(voxel_idx, sdf, weight, color);
                }
            }
        }
    }

    // 第二尺度：中等更新（中间区域）
    int medium_radius = static_cast<int>(std::ceil(truncation_distance_ / voxel_size_ * 0.8f)) + 1;

    for (int dx = -medium_radius; dx <= medium_radius; dx += 2) {
        for (int dy = -medium_radius; dy <= medium_radius; dy += 2) {
            for (int dz = -medium_radius; dz <= medium_radius; dz += 2) {
                VoxelIndex voxel_idx(center_voxel.x + dx, center_voxel.y + dy, center_voxel.z + dz);

                // 跳过已在精细尺度处理的体素
                if (std::abs(dx) <= fine_radius && std::abs(dy) <= fine_radius && std::abs(dz) <= fine_radius) {
                    continue;
                }

                Eigen::Vector3f voxel_center = voxelToWorld(voxel_idx);
                float distance_to_surface = (voxel_center - surface_point).norm();

                if (distance_to_surface <= truncation_distance_ * 0.8f) {
                    Eigen::Vector3f to_surface = surface_point - voxel_center;
                    float sdf = to_surface.dot(surface_normal);

                    sdf = std::max(-truncation_distance_, std::min(truncation_distance_, sdf));
                    sdf /= truncation_distance_;

                    float weight = calculateAdaptiveWeight(sdf, depth) * 0.7f;
                    updateTSDFVoxel(voxel_idx, sdf, weight, color);
                }
            }
        }
    }

    // 第三尺度：粗糙更新（外围区域）
    int coarse_radius = static_cast<int>(std::ceil(truncation_distance_ / voxel_size_ * 1.5f)) + 1;

    for (int dx = -coarse_radius; dx <= coarse_radius; dx += 3) {
        for (int dy = -coarse_radius; dy <= coarse_radius; dy += 3) {
            for (int dz = -coarse_radius; dz <= coarse_radius; dz += 3) {
                VoxelIndex voxel_idx(center_voxel.x + dx, center_voxel.y + dy, center_voxel.z + dz);

                // 跳过已在前两个尺度处理的体素
                if (std::abs(dx) <= medium_radius && std::abs(dy) <= medium_radius && std::abs(dz) <= medium_radius) {
                    continue;
                }

                Eigen::Vector3f voxel_center = voxelToWorld(voxel_idx);
                float distance_to_surface = (voxel_center - surface_point).norm();

                if (distance_to_surface <= truncation_distance_) {
                    Eigen::Vector3f to_surface = surface_point - voxel_center;
                    float sdf = to_surface.dot(surface_normal);

                    sdf = std::max(-truncation_distance_, std::min(truncation_distance_, sdf));
                    sdf /= truncation_distance_;

                    float weight = calculateAdaptiveWeight(sdf, depth) * 0.5f;
                    updateTSDFVoxel(voxel_idx, sdf, weight, color);
                }
            }
        }
    }
}

void TSDFFusion::buildTopologicalConnections() {
    // 拓扑连接构建：确保表面连续性，消除垂直柱状结构
    std::vector<VoxelIndex> surface_voxels;

    // 收集所有表面体素
    for (const auto& pair : tsdf_volume_) {
        const VoxelIndex& idx = pair.first;
        const TSDFVoxel& voxel = pair.second;

        if (voxel.weight > 1.0f && std::abs(voxel.tsdf_value) < 0.3f) {
            surface_voxels.push_back(idx);
        }
    }

    // 分析表面连续性并修复断裂
    for (const auto& voxel_idx : surface_voxels) {
        if (!validateSurfaceContinuity(voxel_idx)) {
            // 修复表面断裂：在相邻体素间插值
            repairSurfaceGap(voxel_idx);
        }
    }

    ROS_INFO_THROTTLE(30.0, "拓扑连接构建完成，处理了 %zu 个表面体素", surface_voxels.size());
}

bool TSDFFusion::validateSurfaceContinuity(const VoxelIndex& voxel_idx) const {
    // 检查相邻体素的连接性
    int connected_neighbors = 0;

    // 检查6连通邻域
    std::vector<VoxelIndex> neighbors = {
        VoxelIndex(voxel_idx.x + 1, voxel_idx.y, voxel_idx.z),
        VoxelIndex(voxel_idx.x - 1, voxel_idx.y, voxel_idx.z),
        VoxelIndex(voxel_idx.x, voxel_idx.y + 1, voxel_idx.z),
        VoxelIndex(voxel_idx.x, voxel_idx.y - 1, voxel_idx.z),
        VoxelIndex(voxel_idx.x, voxel_idx.y, voxel_idx.z + 1),
        VoxelIndex(voxel_idx.x, voxel_idx.y, voxel_idx.z - 1)
    };

    for (const auto& neighbor_idx : neighbors) {
        auto it = tsdf_volume_.find(neighbor_idx);
        if (it != tsdf_volume_.end()) {
            const TSDFVoxel& neighbor = it->second;
            if (neighbor.weight > 1.0f && std::abs(neighbor.tsdf_value) < 0.4f) {
                connected_neighbors++;
            }
        }
    }

    // 如果连接的邻居少于2个，认为存在表面断裂
    return connected_neighbors >= 2;
}

void TSDFFusion::repairSurfaceGap(const VoxelIndex& voxel_idx) {
    // 修复表面断裂：在相邻体素间插值
    auto center_it = tsdf_volume_.find(voxel_idx);
    if (center_it == tsdf_volume_.end()) {
        return;
    }

    const TSDFVoxel& center_voxel = center_it->second;

    // 查找最近的有效表面体素
    std::vector<std::pair<VoxelIndex, float>> valid_neighbors;

    // 扩大搜索范围到3x3x3邻域
    for (int dx = -1; dx <= 1; dx++) {
        for (int dy = -1; dy <= 1; dy++) {
            for (int dz = -1; dz <= 1; dz++) {
                if (dx == 0 && dy == 0 && dz == 0) continue;

                VoxelIndex neighbor_idx(voxel_idx.x + dx, voxel_idx.y + dy, voxel_idx.z + dz);
                auto it = tsdf_volume_.find(neighbor_idx);
                if (it != tsdf_volume_.end()) {
                    const TSDFVoxel& neighbor = it->second;
                    if (neighbor.weight > 1.0f && std::abs(neighbor.tsdf_value) < 0.4f) {
                        float distance = std::sqrt(dx*dx + dy*dy + dz*dz);
                        valid_neighbors.push_back(std::make_pair(neighbor_idx, distance));
                    }
                }
            }
        }
    }

    // 如果找到有效邻居，进行插值修复
    if (valid_neighbors.size() >= 2) {
        // 按距离排序
        std::sort(valid_neighbors.begin(), valid_neighbors.end(),
                 [](const std::pair<VoxelIndex, float>& a, const std::pair<VoxelIndex, float>& b) {
                     return a.second < b.second;
                 });

        // 使用最近的2-3个邻居进行插值
        float interpolated_tsdf = 0.0f;
        float total_weight = 0.0f;
        int interpolation_count = std::min(3, static_cast<int>(valid_neighbors.size()));

        for (int i = 0; i < interpolation_count; i++) {
            const VoxelIndex& neighbor_idx = valid_neighbors[i].first;
            float distance = valid_neighbors[i].second;
            auto neighbor_it = tsdf_volume_.find(neighbor_idx);

            if (neighbor_it != tsdf_volume_.end()) {
                const TSDFVoxel& neighbor = neighbor_it->second;
                float weight = neighbor.weight / (1.0f + distance);  // 距离加权
                interpolated_tsdf += neighbor.tsdf_value * weight;
                total_weight += weight;
            }
        }

        if (total_weight > 0) {
            interpolated_tsdf /= total_weight;

            // 创建插值体素填补间隙
            TSDFVoxel gap_voxel;
            gap_voxel.tsdf_value = interpolated_tsdf;
            gap_voxel.weight = std::min(center_voxel.weight * 0.8f, 5.0f);  // 适当降低权重
            gap_voxel.r = center_voxel.r;
            gap_voxel.g = center_voxel.g;
            gap_voxel.b = center_voxel.b;

            // 在中间位置创建连接体素
            for (int i = 0; i < std::min(2, static_cast<int>(valid_neighbors.size())); i++) {
                const VoxelIndex& neighbor_idx = valid_neighbors[i].first;

                // 在当前体素和邻居之间插入连接体素
                VoxelIndex bridge_idx(
                    (voxel_idx.x + neighbor_idx.x) / 2,
                    (voxel_idx.y + neighbor_idx.y) / 2,
                    (voxel_idx.z + neighbor_idx.z) / 2
                );

                // 只有当桥接位置没有体素时才创建
                if (tsdf_volume_.find(bridge_idx) == tsdf_volume_.end()) {
                    tsdf_volume_[bridge_idx] = gap_voxel;
                }
            }
        }
    }
}

void TSDFFusion::smoothSurfaceTransitions() {
    // 表面平滑处理：减少噪声和不连续
    std::vector<std::pair<VoxelIndex, TSDFVoxel>> updates;

    for (const auto& pair : tsdf_volume_) {
        const VoxelIndex& idx = pair.first;
        const TSDFVoxel& voxel = pair.second;

        if (voxel.weight > 1.0f && std::abs(voxel.tsdf_value) < 0.5f) {
            // 计算邻域平均值
            float avg_tsdf = 0.0f;
            float total_weight = 0.0f;
            int neighbor_count = 0;

            // 3x3x3邻域平滑
            for (int dx = -1; dx <= 1; dx++) {
                for (int dy = -1; dy <= 1; dy++) {
                    for (int dz = -1; dz <= 1; dz++) {
                        VoxelIndex neighbor_idx(idx.x + dx, idx.y + dy, idx.z + dz);
                        auto it = tsdf_volume_.find(neighbor_idx);
                        if (it != tsdf_volume_.end()) {
                            const TSDFVoxel& neighbor = it->second;
                            if (neighbor.weight > 0.5f) {
                                avg_tsdf += neighbor.tsdf_value * neighbor.weight;
                                total_weight += neighbor.weight;
                                neighbor_count++;
                            }
                        }
                    }
                }
            }

            if (neighbor_count > 3 && total_weight > 0) {
                avg_tsdf /= total_weight;

                // 平滑更新
                TSDFVoxel smoothed_voxel = voxel;
                smoothed_voxel.tsdf_value = 0.7f * voxel.tsdf_value + 0.3f * avg_tsdf;
                updates.push_back(std::make_pair(idx, smoothed_voxel));
            }
        }
    }

    // 应用平滑更新
    for (const auto& update : updates) {
        tsdf_volume_[update.first] = update.second;
    }

    ROS_INFO_THROTTLE(30.0, "表面平滑处理完成，更新了 %zu 个体素", updates.size());
}

void TSDFFusion::initializeCoordinateTransforms() {
    // 优化的坐标系转换矩阵
    // 光学坐标系定义: X右, Y下, Z前 (右手坐标系)
    // 机械坐标系定义: X前, Y左, Z上 (ROS标准右手坐标系)

    // 光学坐标系到机械坐标系的转换矩阵
    // 数学验证:
    // X_mechanical = Z_optical (前方向)
    // Y_mechanical = -X_optical (左方向)
    // Z_mechanical = -Y_optical (上方向)
    optical_to_mechanical_rotation_ << 0, 0, 1,    // [0 0 1] * [X Y Z]_optical = Z_optical = X_mechanical
                                      -1, 0, 0,    // [-1 0 0] * [X Y Z]_optical = -X_optical = Y_mechanical
                                       0,-1, 0;    // [0 -1 0] * [X Y Z]_optical = -Y_optical = Z_mechanical

    // 机械坐标系到光学坐标系的转换矩阵（逆变换）
    // 由于是正交矩阵，逆矩阵等于转置矩阵
    mechanical_to_optical_rotation_ = optical_to_mechanical_rotation_.transpose();

    // 验证转换矩阵的正交性
    Eigen::Matrix3f identity_check = optical_to_mechanical_rotation_ * mechanical_to_optical_rotation_;
    float orthogonality_error = (identity_check - Eigen::Matrix3f::Identity()).norm();

    if (orthogonality_error > 1e-6) {
        ROS_WARN("坐标系转换矩阵正交性检查失败，误差: %f", orthogonality_error);
    } else {
        ROS_INFO("坐标系转换矩阵正交性验证通过，误差: %e", orthogonality_error);
    }

    ROS_INFO("增强的坐标系转换矩阵已初始化");
    ROS_INFO("光学坐标系 -> 机械坐标系转换矩阵:");
    ROS_INFO("  [%6.2f %6.2f %6.2f]",
             optical_to_mechanical_rotation_(0,0), optical_to_mechanical_rotation_(0,1), optical_to_mechanical_rotation_(0,2));
    ROS_INFO("  [%6.2f %6.2f %6.2f]",
             optical_to_mechanical_rotation_(1,0), optical_to_mechanical_rotation_(1,1), optical_to_mechanical_rotation_(1,2));
    ROS_INFO("  [%6.2f %6.2f %6.2f]",
             optical_to_mechanical_rotation_(2,0), optical_to_mechanical_rotation_(2,1), optical_to_mechanical_rotation_(2,2));
}

void TSDFFusion::initializeTSDFToRTABMapAlignment() {
    // TSDF到RTAB-Map坐标系对齐变换矩阵
    // 目标：将TSDF建图方向从绕X轴转为绕Z轴，与RTAB-Map保持一致

    // 分析：
    // - RTAB-Map在水平面（XY平面）建图，Z轴向上
    // - TSDF当前在垂直面建图，需要90度旋转校正

    // 变换策略：绕Y轴旋转90度，将Z轴转向X轴方向
    // 旋转矩阵：R_y(90°) = [cos(90°) 0 sin(90°); 0 1 0; -sin(90°) 0 cos(90°)]
    //                    = [0 0 1; 0 1 0; -1 0 0]

    tsdf_to_rtabmap_alignment_ << 0, 0, 1, 0,    // X' = Z
                                  0, 1, 0, 0,    // Y' = Y
                                 -1, 0, 0, 0,    // Z' = -X
                                  0, 0, 0, 1;    // 齐次坐标

    ROS_INFO("🎯 TSDF到RTAB-Map坐标系对齐矩阵初始化完成");
    ROS_INFO("   变换效果: X'=Z, Y'=Y, Z'=-X (绕Y轴旋转90度)");
}

void TSDFFusion::initializeDepthPointcloudTransform() {
    // 深度点云专用变换矩阵初始化
    // 目标：修正TSDF深度点云的垂直柱状结构，使其在世界坐标系xy面上呈现平滑曲面

    // 从参数服务器获取深度点云变换控制参数
    nh_.param("enable_depth_pointcloud_transform", enable_depth_pointcloud_transform_, true);

    if (!enable_depth_pointcloud_transform_) {
        ROS_INFO("🔄 深度点云变换已禁用");
        depth_pointcloud_transform_ = Eigen::Matrix3f::Identity();
        return;
    }

    // 分析当前问题：
    // - 位姿订阅中心矩阵: [0 0 1; -1 0 0; 0 -1 0] (光学到机械坐标系)
    // - TSDF节点矩阵: [0 0 1; 0 1 0; -1 0 0] (TSDF到RTAB-Map对齐)
    // - 复合变换: [0 -1 0; -1 0 0; 0 0 -1] (成功实现xy面建图)
    // - 问题：深度点云仍呈垂直柱状，需要额外的深度方向校正

    // 深度点云校正策略（基于用户数学分析）：
    // 问题：复合变换M_total = [0 -1 0; -1 0 0; 0 0 -1]导致Z_w = -Z_c（深度值）
    // 解决：添加深度变换M_depth，将相机坐标系深度正确映射到世界坐标系

    // 深度点云变换矩阵（方案A：强化重新定向）：
    // 基于用户观察：垂直层叠仍存在，需要更强的坐标重新定向
    // 新策略：将深度方向(Z_c)完全映射到水平面，消除垂直堆叠
    depth_pointcloud_transform_ << 1,  0,  0,    // X' = X_c (保持X方向)
                                   0,  0,  1,    // Y' = Z_c (深度→Y水平方向)
                                   0, -1,  0;    // Z' = -Y_c (相机Y→垂直方向)

    ROS_INFO("🎯 深度点云变换矩阵初始化完成 (方案A)");
    ROS_INFO("   变换目标: 消除垂直层叠结构 -> 深度映射到水平面");
    ROS_INFO("   变换效果: X'=X_c, Y'=Z_c, Z'=-Y_c (强化重新定向)");
    ROS_INFO("   深度点云变换矩阵:");
    ROS_INFO("     [%6.2f %6.2f %6.2f]",
             depth_pointcloud_transform_(0,0), depth_pointcloud_transform_(0,1), depth_pointcloud_transform_(0,2));
    ROS_INFO("     [%6.2f %6.2f %6.2f]",
             depth_pointcloud_transform_(1,0), depth_pointcloud_transform_(1,1), depth_pointcloud_transform_(1,2));
    ROS_INFO("     [%6.2f %6.2f %6.2f]",
             depth_pointcloud_transform_(2,0), depth_pointcloud_transform_(2,1), depth_pointcloud_transform_(2,2));
}

Eigen::Vector3f TSDFFusion::applyCoordinateTransform(const Eigen::Vector3f& point,
                                                   CoordinateFrameType from_type,
                                                   CoordinateFrameType to_type) const {
    // 禁用内部坐标变换时，直接返回原始点
    if (disable_internal_coordinate_transform_) {
        ROS_DEBUG_THROTTLE(60.0, "🎯 内部坐标变换已禁用，信任位姿订阅中心变换结果");
        return point;
    }

    if (from_type == to_type) {
        return point;
    }

    if (from_type == CoordinateFrameType::OPTICAL && to_type == CoordinateFrameType::MECHANICAL) {
        return optical_to_mechanical_rotation_ * point;
    } else if (from_type == CoordinateFrameType::MECHANICAL && to_type == CoordinateFrameType::OPTICAL) {
        return mechanical_to_optical_rotation_ * point;
    }

    return point;  // 未知类型，不进行转换
}





pcl::PointCloud<pcl::PointXYZRGB>::Ptr TSDFFusion::generateMesh() {
    ROS_ERROR("🔧 [DEBUG] generateMesh: 函数被调用！");
    pcl::PointCloud<pcl::PointXYZRGB>::Ptr cloud(new pcl::PointCloud<pcl::PointXYZRGB>);

    // 🚀 优先使用VoxBlox存储生成点云
    ROS_ERROR("🔧 [DEBUG] 检查VoxBlox存储: use_voxblox_storage_=%s, voxblox_adapter_=%s",
              use_voxblox_storage_ ? "true" : "false", voxblox_adapter_ ? "valid" : "null");
    if (use_voxblox_storage_ && voxblox_adapter_) {
        ROS_ERROR("🔧 [DEBUG] 使用VoxBlox生成点云...");
        // 🚀 使用配置的密度增强参数
        float weight_threshold = enable_density_enhancement_ ? voxblox_min_weight_threshold_ : 0.01f;
        cloud = voxblox_adapter_->generatePointCloud(weight_threshold);

        if (cloud && cloud->points.size() > 0) {
            cloud->header.frame_id = world_frame_;
            cloud->header.stamp = pcl_conversions::toPCL(ros::Time::now());

            ROS_INFO("🚀 VoxBlox点云生成成功: %zu 个点", cloud->points.size());
            return cloud;
        } else {
            ROS_WARN("⚠️ VoxBlox点云生成失败，回退到传统方法");
        }
    }

    // 🔄 传统方法（备用方案）
    // 在生成点云前进行表面后处理
    static int processing_counter = 0;
    processing_counter++;

    // 每10帧进行一次拓扑连接构建和表面平滑
    if (processing_counter % 10 == 0) {
        buildTopologicalConnections();
        smoothSurfaceTransitions();
    }

    // 调试：统计TSDF体素信息
    int total_voxels = tsdf_volume_.size();
    int valid_weight_voxels = 0;
    int surface_voxels = 0;
    float min_tsdf = std::numeric_limits<float>::max();
    float max_tsdf = std::numeric_limits<float>::lowest();

    // 增强的点云生成 - 多层次表面提取
    for (const auto& pair : tsdf_volume_) {
        const VoxelIndex& idx = pair.first;
        const TSDFVoxel& voxel = pair.second;

        // 调试统计 - 🚀 降低权重阈值以包含更多体素
        if (voxel.weight > 0.5f) {  // 从1.0f降低到0.5f
            valid_weight_voxels++;
            min_tsdf = std::min(min_tsdf, std::abs(voxel.tsdf_value));
            max_tsdf = std::max(max_tsdf, std::abs(voxel.tsdf_value));
        }

        // 使用自适应阈值进行表面提取 - 🚀 降低权重要求
        float adaptive_threshold = calculateAdaptiveSurfaceThreshold(voxel, idx);
        if (voxel.weight > 0.5f && std::abs(voxel.tsdf_value) < adaptive_threshold) {  // 从1.0f降低到0.5f
            surface_voxels++;
            pcl::PointXYZRGB point;
            Eigen::Vector3f world_pos = voxelToWorld(idx);

            // 直接使用世界坐标（变换已在深度数据源头应用）
            point.x = world_pos.x();
            point.y = world_pos.y();
            point.z = world_pos.z();
            point.r = voxel.r;
            point.g = voxel.g;
            point.b = voxel.b;

            cloud->points.push_back(point);
        }
    }

    cloud->width = cloud->points.size();
    cloud->height = 1;
    cloud->is_dense = false;

    // 深度点云变换效果统计和调试输出
    if (enable_depth_pointcloud_transform_ && !cloud->points.empty()) {
        // 计算点云的空间分布统计
        float min_x = std::numeric_limits<float>::max();
        float max_x = std::numeric_limits<float>::lowest();
        float min_y = std::numeric_limits<float>::max();
        float max_y = std::numeric_limits<float>::lowest();
        float min_z = std::numeric_limits<float>::max();
        float max_z = std::numeric_limits<float>::lowest();

        for (const auto& point : cloud->points) {
            min_x = std::min(min_x, point.x);
            max_x = std::max(max_x, point.x);
            min_y = std::min(min_y, point.y);
            max_y = std::max(max_y, point.y);
            min_z = std::min(min_z, point.z);
            max_z = std::max(max_z, point.z);
        }

        ROS_INFO_THROTTLE(10.0, "🎯 深度点云变换效果统计:");
        ROS_INFO_THROTTLE(10.0, "   点云数量: %zu", cloud->points.size());
        ROS_INFO_THROTTLE(10.0, "   X范围: [%.3f, %.3f] (跨度: %.3f)", min_x, max_x, max_x - min_x);
        ROS_INFO_THROTTLE(10.0, "   Y范围: [%.3f, %.3f] (跨度: %.3f)", min_y, max_y, max_y - min_y);
        ROS_INFO_THROTTLE(10.0, "   Z范围: [%.3f, %.3f] (跨度: %.3f)", min_z, max_z, max_z - min_z);

        // 检查是否成功修正了垂直柱状结构
        float xy_span = std::max(max_x - min_x, max_y - min_y);
        float z_span = max_z - min_z;
        float aspect_ratio = xy_span / (z_span + 1e-6);  // 避免除零

        if (aspect_ratio > 2.0) {
            ROS_INFO_THROTTLE(10.0, "   ✅ 深度点云变换成功：XY跨度/Z跨度 = %.2f (>2.0，呈现平滑曲面)", aspect_ratio);
        } else {
            ROS_WARN_THROTTLE(10.0, "   ⚠️ 深度点云可能仍呈柱状：XY跨度/Z跨度 = %.2f (<2.0)", aspect_ratio);
        }
    }

    // 调试输出TSDF统计信息
    ROS_INFO_THROTTLE(5.0, "TSDF调试统计: 总体素=%d, 有效权重体素=%d, 表面体素=%d, 生成点云=%zu个点",
                     total_voxels, valid_weight_voxels, surface_voxels, cloud->points.size());
    if (valid_weight_voxels > 0) {
        ROS_INFO_THROTTLE(5.0, "TSDF值范围: min=%.3f, max=%.3f", min_tsdf, max_tsdf);
    }

    // 应用统计滤波
    cloud = applyStatisticalFilter(cloud);

    ROS_INFO_THROTTLE(5.0, "滤波后点云: %zu 个点", cloud->points.size());

    return cloud;
}

void TSDFFusion::publishVisualization() {
    // 🔧 强制调试：确认publishVisualization被调用
    static int publish_count = 0;
    publish_count++;
    ROS_ERROR("🔧 [DEBUG] publishVisualization #%d: 函数被调用！", publish_count);

    // 生成并发布点云
    ROS_ERROR("🔧 [DEBUG] 开始生成点云...");
    pcl::PointCloud<pcl::PointXYZRGB>::Ptr cloud = generateMesh();
    ROS_ERROR("🔧 [DEBUG] 点云生成完成，点数: %zu", cloud->points.size());

    // 🚀 增强调试：详细的点云生成统计
    ROS_INFO_THROTTLE(5.0, "🔍 点云生成统计 #%d: 生成了 %zu 个点",
                     publish_count, cloud->points.size());

    if (!cloud->points.empty()) {
        sensor_msgs::PointCloud2 cloud_msg;
        pcl::toROSMsg(*cloud, cloud_msg);
        // 强制坐标系对齐：确保与RTAB-Map完全一致
        cloud_msg.header.frame_id = "map";  // 强制使用map坐标系，与RTAB-Map输出一致

        // 🔧 修复2: 使用最新的RTAB-Map时间戳而不是ros::Time::now()
        if (latest_rtabmap_odom_) {
            cloud_msg.header.stamp = latest_rtabmap_odom_->header.stamp;
            ROS_DEBUG_THROTTLE(10.0, "🔧 使用RTAB-Map时间戳: %.3f", cloud_msg.header.stamp.toSec());
        } else {
            cloud_msg.header.stamp = ros::Time(0);  // 使用最新可用时间
            ROS_DEBUG_THROTTLE(10.0, "🔧 使用最新可用时间戳");
        }

        pointcloud_pub_.publish(cloud_msg);

        ROS_INFO_THROTTLE(10.0, "✅ 发布TSDF点云: %zu个点，坐标系: %s (与RTAB-Map对齐)",
                         cloud->points.size(), cloud_msg.header.frame_id.c_str());

        // 发布质量标记
        publishQualityMarkers(cloud);

        // 🆕 发布体素格式数据
        publishVoxelVisualization();
    } else {
        ROS_WARN_THROTTLE(5.0, "⚠️ 点云生成失败: 0个点 - 检查体素数据和阈值设置");

        // 🔍 诊断信息：输出体素统计
        int total_voxels = 0;
        int valid_voxels = 0;
        int surface_voxels = 0;

        for (const auto& pair : tsdf_volume_) {
            total_voxels++;
            if (pair.second.weight > 0.1f) {  // 有效体素
                valid_voxels++;
                if (std::abs(pair.second.tsdf_value) < 0.1f) {  // 表面体素
                    surface_voxels++;
                }
            }
        }

        ROS_WARN_THROTTLE(5.0, "🔍 体素统计: 总计=%d, 有效=%d, 表面=%d",
                         total_voxels, valid_voxels, surface_voxels);
    }
}

void TSDFFusion::publishVoxelVisualization() {
    // 🆕 发布体素格式可视化数据
    static int voxel_publish_count = 0;
    voxel_publish_count++;

    ROS_INFO_THROTTLE(10.0, "🔲 发布体素可视化 #%d", voxel_publish_count);

    if (use_voxblox_storage_ && voxblox_adapter_) {
        // 使用VoxBlox生成体素标记 - 🚀 使用密度增强参数
        float voxel_weight_threshold = enable_density_enhancement_ ? voxblox_min_weight_threshold_ : 0.1f;
        visualization_msgs::MarkerArray voxel_markers = voxblox_adapter_->generateVoxelMarkers(voxel_weight_threshold);
        if (!voxel_markers.markers.empty()) {
            voxel_markers_pub_.publish(voxel_markers);
            ROS_INFO_THROTTLE(15.0, "✅ 发布VoxBlox体素标记: %zu个标记", voxel_markers.markers.size());
        }

        // 生成体素网格数据并转换为点云格式发布 - 🚀 使用密度增强参数
        auto voxel_grid = voxblox_adapter_->generateVoxelGrid(voxel_weight_threshold);
        if (voxel_grid.total_voxels > 0) {
            // 创建体素网格点云
            pcl::PointCloud<pcl::PointXYZRGB>::Ptr voxel_cloud(new pcl::PointCloud<pcl::PointXYZRGB>);
            voxel_cloud->header.frame_id = world_frame_;
            voxel_cloud->header.stamp = pcl_conversions::toPCL(ros::Time::now());

            for (size_t i = 0; i < voxel_grid.positions.size(); ++i) {
                pcl::PointXYZRGB point;
                point.x = voxel_grid.positions[i].x;
                point.y = voxel_grid.positions[i].y;
                point.z = voxel_grid.positions[i].z;
                point.r = static_cast<uint8_t>(voxel_grid.colors[i].r * 255);
                point.g = static_cast<uint8_t>(voxel_grid.colors[i].g * 255);
                point.b = static_cast<uint8_t>(voxel_grid.colors[i].b * 255);
                voxel_cloud->points.push_back(point);
            }

            voxel_cloud->width = voxel_cloud->points.size();
            voxel_cloud->height = 1;
            voxel_cloud->is_dense = true;

            // 发布体素网格点云
            sensor_msgs::PointCloud2 voxel_grid_msg;
            pcl::toROSMsg(*voxel_cloud, voxel_grid_msg);
            voxel_grid_msg.header.frame_id = world_frame_;
            voxel_grid_msg.header.stamp = ros::Time::now();
            voxel_grid_pub_.publish(voxel_grid_msg);

            ROS_INFO_THROTTLE(15.0, "✅ 发布VoxBlox体素网格: %zu个体素, 体素大小=%.3fm",
                             voxel_grid.total_voxels, voxel_grid.voxel_size);
        }
    } else {
        // 传统存储模式的体素可视化
        visualization_msgs::MarkerArray voxel_markers;

        // 清除之前的标记
        visualization_msgs::Marker clear_marker;
        clear_marker.header.frame_id = world_frame_;
        clear_marker.header.stamp = ros::Time::now();
        clear_marker.ns = "traditional_voxels";
        clear_marker.action = visualization_msgs::Marker::DELETEALL;
        voxel_markers.markers.push_back(clear_marker);

        // 创建传统体素标记
        visualization_msgs::Marker voxel_marker;
        voxel_marker.header.frame_id = world_frame_;
        voxel_marker.header.stamp = ros::Time::now();
        voxel_marker.ns = "traditional_voxels";
        voxel_marker.id = 0;
        voxel_marker.type = visualization_msgs::Marker::CUBE_LIST;
        voxel_marker.action = visualization_msgs::Marker::ADD;
        voxel_marker.pose.orientation.w = 1.0;
        voxel_marker.scale.x = voxel_size_;
        voxel_marker.scale.y = voxel_size_;
        voxel_marker.scale.z = voxel_size_;

        // 添加传统存储的体素
        int voxel_count = 0;
        for (const auto& pair : tsdf_volume_) {
            if (pair.second.weight > 0.1f && voxel_count < 10000) {  // 限制数量避免性能问题
                Eigen::Vector3f world_pos = voxelToWorld(pair.first);

                geometry_msgs::Point point;
                point.x = world_pos.x();
                point.y = world_pos.y();
                point.z = world_pos.z();
                voxel_marker.points.push_back(point);

                std_msgs::ColorRGBA color;
                color.r = pair.second.r / 255.0f;
                color.g = pair.second.g / 255.0f;
                color.b = pair.second.b / 255.0f;
                color.a = 0.6f;
                voxel_marker.colors.push_back(color);

                voxel_count++;
            }
        }

        if (!voxel_marker.points.empty()) {
            voxel_markers.markers.push_back(voxel_marker);
            voxel_markers_pub_.publish(voxel_markers);
            ROS_INFO_THROTTLE(15.0, "✅ 发布传统体素标记: %d个体素", voxel_count);
        }
    }
}

void TSDFFusion::publishQualityMarkers(const pcl::PointCloud<pcl::PointXYZRGB>::Ptr& cloud) {
    visualization_msgs::MarkerArray markers;

    // 创建质量统计标记
    visualization_msgs::Marker quality_marker;
    quality_marker.header.frame_id = world_frame_;

    // 🔧 修复2: 使用RTAB-Map时间戳
    if (latest_rtabmap_odom_) {
        quality_marker.header.stamp = latest_rtabmap_odom_->header.stamp;
    } else {
        quality_marker.header.stamp = ros::Time(0);
    }
    quality_marker.ns = "tsdf_quality";
    quality_marker.id = 0;
    quality_marker.type = visualization_msgs::Marker::TEXT_VIEW_FACING;
    quality_marker.action = visualization_msgs::Marker::ADD;

    quality_marker.pose.position.x = 0;
    quality_marker.pose.position.y = 0;
    quality_marker.pose.position.z = 2.0;
    quality_marker.pose.orientation.w = 1.0;

    quality_marker.scale.z = 0.2;
    quality_marker.color.r = 1.0;
    quality_marker.color.g = 1.0;
    quality_marker.color.b = 0.0;
    quality_marker.color.a = 0.8;

    // 计算质量统计
    size_t total_voxels = tsdf_volume_.size();
    size_t surface_voxels = cloud->points.size();

    std::ostringstream ss;
    ss << "TSDF质量统计:\n";
    ss << "总体素: " << total_voxels << "\n";
    ss << "表面点: " << surface_voxels << "\n";
    ss << "质量优化: 启用";

    quality_marker.text = ss.str();
    markers.markers.push_back(quality_marker);

    marker_pub_.publish(markers);
}

void TSDFFusion::generateTestPointCloud() {
    // 生成测试点云以验证发布功能
    pcl::PointCloud<pcl::PointXYZRGB>::Ptr test_cloud(new pcl::PointCloud<pcl::PointXYZRGB>);

    // 创建一个简单的立方体点云
    for (float x = -0.5; x <= 0.5; x += 0.05) {
        for (float y = -0.5; y <= 0.5; y += 0.05) {
            for (float z = 0.0; z <= 1.0; z += 0.05) {
                pcl::PointXYZRGB point;
                point.x = x;
                point.y = y;
                point.z = z;
                point.r = 0;    // 青色
                point.g = 255;
                point.b = 255;
                test_cloud->points.push_back(point);
            }
        }
    }

    test_cloud->width = test_cloud->points.size();
    test_cloud->height = 1;
    test_cloud->is_dense = true;

    // 发布测试点云
    sensor_msgs::PointCloud2 cloud_msg;
    pcl::toROSMsg(*test_cloud, cloud_msg);
    cloud_msg.header.frame_id = world_frame_;
    cloud_msg.header.stamp = ros::Time::now();
    pointcloud_pub_.publish(cloud_msg);

    ROS_INFO("发布测试点云: %zu 个点", test_cloud->points.size());
}

void TSDFFusion::diagnoseCoordinateAlignment(const std::string& depth_frame_id,
                                           const std::string& effective_camera_frame,
                                           CoordinateFrameType depth_frame_type,
                                           CoordinateFrameType effective_frame_type,
                                           const geometry_msgs::TransformStamped& transform) {
    static int diagnosis_counter = 0;
    diagnosis_counter++;

    // 每100帧进行一次详细诊断
    if (diagnosis_counter % 100 == 0) {
        ROS_INFO("=== 坐标系对齐诊断报告 (第%d帧) ===", diagnosis_counter);
        ROS_INFO("深度图像坐标系: %s (类型: %s)",
                 depth_frame_id.c_str(),
                 (depth_frame_type == CoordinateFrameType::OPTICAL) ? "光学" :
                 (depth_frame_type == CoordinateFrameType::MECHANICAL) ? "机械" : "未知");
        ROS_INFO("有效相机坐标系: %s (类型: %s)",
                 effective_camera_frame.c_str(),
                 (effective_frame_type == CoordinateFrameType::OPTICAL) ? "光学" :
                 (effective_frame_type == CoordinateFrameType::MECHANICAL) ? "机械" : "未知");
        ROS_INFO("TF变换: %s -> %s", world_frame_.c_str(), effective_camera_frame.c_str());
        ROS_INFO("  平移: [%.3f, %.3f, %.3f]",
                 transform.transform.translation.x,
                 transform.transform.translation.y,
                 transform.transform.translation.z);
        ROS_INFO("  旋转: [%.3f, %.3f, %.3f, %.3f]",
                 transform.transform.rotation.x,
                 transform.transform.rotation.y,
                 transform.transform.rotation.z,
                 transform.transform.rotation.w);

        // 检查坐标系一致性
        if (depth_frame_type != effective_frame_type &&
            depth_frame_type != CoordinateFrameType::UNKNOWN &&
            effective_frame_type != CoordinateFrameType::UNKNOWN) {
            ROS_WARN("检测到坐标系类型不一致，将应用智能转换");
            coordinate_mismatch_count_++;
        } else {
            ROS_INFO("坐标系类型一致，无需额外转换");
        }

        ROS_INFO("累计坐标系不匹配次数: %d", coordinate_mismatch_count_);
        ROS_INFO("=== 诊断报告结束 ===");
    }
}

void TSDFFusion::validateCoordinateConfiguration() {
    ROS_INFO("=== 坐标系配置验证 ===");

    // 检测配置的坐标系类型
    CoordinateFrameType world_frame_type = detectCoordinateFrameType(world_frame_);
    CoordinateFrameType camera_frame_type = detectCoordinateFrameType(camera_frame_);

    ROS_INFO("世界坐标系: %s (类型: %s)",
             world_frame_.c_str(),
             (world_frame_type == CoordinateFrameType::OPTICAL) ? "光学" :
             (world_frame_type == CoordinateFrameType::MECHANICAL) ? "机械" : "未知");
    ROS_INFO("相机坐标系: %s (类型: %s)",
             camera_frame_.c_str(),
             (camera_frame_type == CoordinateFrameType::OPTICAL) ? "光学" :
             (camera_frame_type == CoordinateFrameType::MECHANICAL) ? "机械" : "未知");

    // 检查潜在的配置问题
    if (world_frame_type == CoordinateFrameType::UNKNOWN) {
        ROS_WARN("世界坐标系类型未知: %s，建议使用标准名称如'map'", world_frame_.c_str());
    }

    if (camera_frame_type == CoordinateFrameType::UNKNOWN) {
        ROS_WARN("相机坐标系类型未知: %s，将在运行时动态检测", camera_frame_.c_str());
    }

    // 提供配置建议
    if (camera_frame_type == CoordinateFrameType::OPTICAL) {
        ROS_INFO("检测到光学坐标系配置，将自动应用坐标系转换");
        ROS_INFO("建议: 如需与RTAB-Map完全一致，可考虑使用'base_link'作为camera_frame");
    } else if (camera_frame_type == CoordinateFrameType::MECHANICAL) {
        ROS_INFO("检测到机械坐标系配置，与RTAB-Map兼容性良好");
    }

    // 初始化诊断计数器
    coordinate_mismatch_count_ = 0;

    ROS_INFO("=== 配置验证完成 ===");
}

void TSDFFusion::reset() {
    tsdf_volume_.clear();
    camera_info_received_ = false;
    ROS_INFO("TSDF体积已重置");
}

void TSDFFusion::initializeRTABMapCollaboration() {
    if (use_rtabmap_pose_ || enable_rtabmap_collaboration_) {
        // 修复：优先使用RTAB-Map优化后的位姿数据，而非原始里程计数据
        std::vector<std::string> candidate_topics = {
            "/rtabmap/odom",           // 标准RTAB-Map优化位姿（优先）
            "/stereo_camera/odom",     // ZED相机里程计
            "/odom"                    // 通用里程计话题（bag文件中的数据，备用）
        };

        // 位姿订阅中心融合 - 实现流程图设计的订阅位姿式融合
        std::string selected_topic = "/pose_center/odom";  // 使用位姿订阅中心的标准化位姿数据
        rtabmap_odom_sub_ = nh_.subscribe(selected_topic, 10,
                                         &TSDFFusion::rtabmapOdomCallback, this);
        ROS_INFO("✅ 已启用位姿订阅中心融合，订阅话题: %s", selected_topic.c_str());
        ROS_INFO("  use_rtabmap_pose: %s", use_rtabmap_pose_ ? "是" : "否");
        ROS_INFO("  enable_collaboration: %s", enable_rtabmap_collaboration_ ? "是" : "否");
        ROS_INFO("  位姿数据源: RTAB-Map优化位姿（算法级融合）");
        ROS_INFO("  备选话题: /stereo_camera/odom, /odom");

        // 添加位姿数据质量监控
        ROS_INFO("  位姿质量监控: 启用时间戳同步验证");
    } else {
        ROS_INFO("RTAB-Map位姿融合已禁用，使用传统TF变换");
    }
}

void TSDFFusion::rtabmapOdomCallback(const nav_msgs::Odometry::ConstPtr& odom_msg) {
    std::lock_guard<std::mutex> lock(rtabmap_odom_mutex_);

    // 实时性优化：验证位姿数据质量
    if (!odom_msg) {
        ROS_WARN("收到空的RTAB-Map位姿消息，跳过处理");
        return;
    }

    // 检查位姿数据的合理性
    double pos_magnitude = sqrt(pow(odom_msg->pose.pose.position.x, 2) +
                               pow(odom_msg->pose.pose.position.y, 2) +
                               pow(odom_msg->pose.pose.position.z, 2));
    if (pos_magnitude > 1000.0) {  // 位置超过1000米可能异常
        ROS_WARN_THROTTLE(5.0, "检测到异常位姿数据: 位置幅度=%.2fm，可能存在坐标系问题", pos_magnitude);
    }

    latest_rtabmap_odom_ = odom_msg;
    last_rtabmap_odom_time_ = odom_msg->header.stamp;

    // 实时性监控：记录位姿更新频率
    static ros::Time last_update_time = ros::Time::now();
    static int update_count = 0;
    update_count++;

    if (update_count % 50 == 0) {
        double update_interval = (ros::Time::now() - last_update_time).toSec() / 50.0;
        double update_rate = 1.0 / update_interval;
        ROS_INFO_THROTTLE(10.0, "TSDF位姿更新频率: %.1f Hz", update_rate);
        last_update_time = ros::Time::now();
    }

    // 坐标系匹配验证
    static bool coordinate_verified = false;
    if (!coordinate_verified) {
        if (odom_msg->child_frame_id == camera_frame_) {
            ROS_INFO("✓ 坐标系匹配验证成功: RTAB-Map child_frame_id='%s' == TSDF camera_frame='%s'",
                     odom_msg->child_frame_id.c_str(), camera_frame_.c_str());
        } else {
            ROS_WARN("⚠ 坐标系不匹配检测: RTAB-Map child_frame_id='%s' != TSDF camera_frame='%s'",
                     odom_msg->child_frame_id.c_str(), camera_frame_.c_str());
            ROS_WARN("  这可能导致垂直错落问题，建议统一坐标系配置");
        }
        coordinate_verified = true;
    }

    // 调试信息（限制频率）
    static int callback_count = 0;
    callback_count++;
    if (callback_count % 30 == 0) {  // 每30次回调输出一次
        ROS_INFO("收到RTAB-Map位姿更新: 位置(%.2f, %.2f, %.2f), 时间戳: %.3f",
                 odom_msg->pose.pose.position.x,
                 odom_msg->pose.pose.position.y,
                 odom_msg->pose.pose.position.z,
                 odom_msg->header.stamp.toSec());
    }
}

bool TSDFFusion::getRTABMapPose(const ros::Time& target_time, geometry_msgs::TransformStamped& transform) {
    std::lock_guard<std::mutex> lock(rtabmap_odom_mutex_);

    // 增强调试：详细记录位姿获取过程
    static int pose_request_count = 0;
    pose_request_count++;

    ROS_INFO_THROTTLE(2.0, "🔍 位姿请求 #%d: 目标时间=%.3f, 算法级融合架构",
                     pose_request_count, target_time.toSec());

    if (!latest_rtabmap_odom_) {
        ROS_WARN_THROTTLE(5.0, "❌ 位姿请求 #%d: 尚未收到RTAB-Map位姿数据，等待位姿订阅中心发布", pose_request_count);
        static bool first_warning = true;
        if (first_warning) {
            ROS_WARN("TSDF实时性优化：启用位姿预测模式以减少等待时间");
            first_warning = false;
        }
        return false;
    }

    ROS_INFO_THROTTLE(3.0, "✅ 位姿请求 #%d: 已有位姿数据，时间戳=%.3f",
                     pose_request_count, latest_rtabmap_odom_->header.stamp.toSec());

    // 检查时间戳差异 - 实时性优化：进一步增加容忍度以解决bag文件回放同步问题
    double time_diff = std::abs((target_time - last_rtabmap_odom_time_).toSec());
    double time_tolerance;
    nh_.param<double>("pose_time_tolerance", time_tolerance, 5.0);  // 从2.0秒增加到5.0秒

    if (time_diff > time_tolerance) {
        ROS_WARN_THROTTLE(5.0, "RTAB-Map位姿数据过旧: 时间差=%.3f秒 (容忍度=%.1f秒)", time_diff, time_tolerance);
        return false;
    }

    // 实时性监控：输出时间戳同步状态
    ROS_DEBUG_THROTTLE(10.0, "位姿时间戳同步: 目标时间=%.3f, RTAB-Map时间=%.3f, 差异=%.3f秒",
                      target_time.toSec(), last_rtabmap_odom_time_.toSec(), time_diff);

    // 实时性统计：记录位姿获取成功率
    static int pose_success_count = 0;

    if (time_diff <= time_tolerance) {
        pose_success_count++;

        // 每100次请求输出一次成功率统计
        if (pose_request_count % 100 == 0) {
            double success_rate = (double)pose_success_count / pose_request_count * 100.0;
            ROS_INFO("TSDF位姿获取统计: 成功率=%.1f%% (%d/%d), 平均时间差=%.3f秒",
                     success_rate, pose_success_count, pose_request_count, time_diff);
        }
    }

    // 构造变换 - 简化版本，直接使用RTAB-Map位姿
    transform.header.stamp = latest_rtabmap_odom_->header.stamp;
    transform.header.frame_id = "map";  // 强制使用map坐标系
    transform.child_frame_id = "base_link";  // 使用base_link作为目标

    // 直接使用RTAB-Map的位姿数据，无需复杂的坐标系转换
    transform.transform.translation.x = latest_rtabmap_odom_->pose.pose.position.x;
    transform.transform.translation.y = latest_rtabmap_odom_->pose.pose.position.y;
    transform.transform.translation.z = latest_rtabmap_odom_->pose.pose.position.z;
    transform.transform.rotation = latest_rtabmap_odom_->pose.pose.orientation;

    // 调试输出位姿信息
    ROS_DEBUG_THROTTLE(30.0, "✅ 位姿订阅中心融合：获取位姿 [%.3f, %.3f, %.3f]",
                 transform.transform.translation.x,
                 transform.transform.translation.y,
                 transform.transform.translation.z);

    return true;
}



// ============================================================================
// 🚀 GPU加速相关方法实现（保持算法级融合架构）
// ============================================================================

bool TSDFFusion::initializeGPUAcceleration() {
    if (!gpu_tsdf_) {
        ROS_ERROR("❌ GPU TSDF对象未创建");
        return false;
    }

    ROS_INFO("🚀 初始化GPU加速TSDF...");

    // 配置GPU TSDF参数
    CudaTSDFParams gpu_params;
    gpu_params.voxel_size = voxel_size_;
    gpu_params.truncation_distance = truncation_distance_;
    gpu_params.max_weight = max_weight_;

    // 设置体积大小（基于截断距离估算）
    float volume_extent = 10.0f;  // 10米范围
    gpu_params.volume_size_x = static_cast<int>(volume_extent / voxel_size_);
    gpu_params.volume_size_y = static_cast<int>(volume_extent / voxel_size_);
    gpu_params.volume_size_z = static_cast<int>(volume_extent / voxel_size_);

    // 🔧 动态体积原点：初始化为机器人当前位置为中心
    // 这将在每帧处理时根据机器人位置动态更新
    gpu_params.volume_origin_x = -volume_extent / 2.0f;
    gpu_params.volume_origin_y = -volume_extent / 2.0f;
    gpu_params.volume_origin_z = -volume_extent / 2.0f;

    // 配置相机参数（将在收到camera_info后更新）
    CudaCameraParams camera_params;
    camera_params.width = 640;   // 默认值，将被实际值覆盖
    camera_params.height = 480;
    camera_params.fx = 500.0f;
    camera_params.fy = 500.0f;
    camera_params.cx = 320.0f;
    camera_params.cy = 240.0f;
    camera_params.depth_min = depth_min_;
    camera_params.depth_max = depth_max_;

    // 初始化GPU TSDF
    if (gpu_tsdf_->initialize(gpu_params, camera_params)) {
        gpu_initialized_ = true;
        ROS_INFO("✅ GPU加速TSDF初始化成功");

        // 🚀 设置GPU迁移距离阈值
        gpu_tsdf_->setMigrationDistanceThreshold(migration_distance_threshold_);
        ROS_INFO("🚀 GPU迁移距离阈值已设置: %.1f 体素单位", migration_distance_threshold_);

        // 输出GPU内存使用情况
        size_t used_mem, total_mem;
        gpu_tsdf_->getMemoryUsage(used_mem, total_mem);
        ROS_INFO("🚀 GPU内存使用: %.1fMB / %.1fMB",
                 used_mem / (1024.0f * 1024.0f),
                 total_mem / (1024.0f * 1024.0f));

        return true;
    } else {
        ROS_ERROR("❌ GPU加速TSDF初始化失败");
        return false;
    }
}

bool TSDFFusion::processRGBDWithGPU(const sensor_msgs::ImageConstPtr& rgb_msg,
                                    const sensor_msgs::ImageConstPtr& depth_msg,
                                    const sensor_msgs::CameraInfoConstPtr& camera_info,
                                    const Eigen::Matrix4f& camera_pose) {
    if (!gpu_tsdf_ || !gpu_initialized_) {
        ROS_ERROR("❌ GPU TSDF未初始化");
        return false;
    }

    try {
        // 转换图像格式
        cv_bridge::CvImagePtr rgb_ptr = cv_bridge::toCvCopy(rgb_msg, sensor_msgs::image_encodings::BGR8);
        cv_bridge::CvImagePtr depth_ptr = cv_bridge::toCvCopy(depth_msg, sensor_msgs::image_encodings::TYPE_32FC1);

        cv::Mat rgb_image = rgb_ptr->image;
        cv::Mat depth_image = depth_ptr->image;

        // 🎯 保持算法级融合架构：使用来自位姿订阅中心的变换矩阵
        ROS_DEBUG_THROTTLE(10.0, "🎯 GPU处理：使用位姿订阅中心的变换矩阵 [%.3f, %.3f, %.3f]",
                          camera_pose(0,3), camera_pose(1,3), camera_pose(2,3));

        // 🔧 关键修复：在GPU处理前同步体素原点参数
        if (volume_origin_initialized_) {
            gpu_tsdf_->updateVolumeOrigin(volume_origin_.x(), volume_origin_.y(), volume_origin_.z());
            ROS_DEBUG_THROTTLE(10.0, "🔧 GPU体素原点已同步: [%.3f, %.3f, %.3f]",
                              volume_origin_.x(), volume_origin_.y(), volume_origin_.z());
        }

        // 调用GPU加速处理
        ROS_ERROR("🔧 [DEBUG] 调用gpu_tsdf_->processRGBD...");
        bool success = gpu_tsdf_->processRGBD(rgb_image, depth_image, camera_pose);
        ROS_ERROR("🔧 [DEBUG] gpu_tsdf_->processRGBD返回: %s", success ? "成功" : "失败");

        if (success) {
            // 获取性能统计
            auto perf_stats = gpu_tsdf_->getPerformanceStats();
            ROS_INFO_THROTTLE(10.0, "🚀 GPU性能: 处理时间=%.2fms, 帧数=%d, 平均FPS=%.1f",
                             perf_stats.gpu_process_time_ms,
                             perf_stats.processed_frames,
                             perf_stats.average_fps);

            // 🔄 混合策略：从GPU传输体素数据到CPU（每帧都传输以确保实时性）
            static int transfer_counter = 0;
            transfer_counter++;

            // 每帧都传输以确保点云数据实时更新
            ROS_ERROR("🔧 [DEBUG] 开始transferGPUVoxelsToCPU...");
            transferGPUVoxelsToCPU();
            ROS_ERROR("🔧 [DEBUG] transferGPUVoxelsToCPU完成");
            ROS_DEBUG_THROTTLE(5.0, "🔄 GPU到CPU体素数据传输完成 (第%d帧)", transfer_counter);
        }

        return success;

    } catch (cv_bridge::Exception& e) {
        ROS_ERROR("❌ GPU处理图像转换失败: %s", e.what());
        return false;
    } catch (const std::exception& e) {
        ROS_ERROR("❌ GPU处理异常: %s", e.what());
        return false;
    }
}

void TSDFFusion::cleanupGPUResources() {
    if (gpu_tsdf_) {
        gpu_tsdf_.reset();
        gpu_initialized_ = false;
        ROS_INFO("🚀 GPU资源已清理");
    }
}

bool TSDFFusion::isGPUAvailable() const {
    return enable_gpu_acceleration_ && gpu_initialized_;
}

void TSDFFusion::getGPUPerformanceStats() const {
    if (!gpu_tsdf_ || !gpu_initialized_) {
        ROS_WARN("⚠️ GPU TSDF未初始化，无法获取性能统计");
        return;
    }

    auto stats = gpu_tsdf_->getPerformanceStats();

    ROS_INFO("🚀 GPU性能统计:");
    ROS_INFO("   处理时间: %.2f ms", stats.gpu_process_time_ms);
    ROS_INFO("   内存传输时间: %.2f ms", stats.memory_transfer_time_ms);
    ROS_INFO("   总时间: %.2f ms", stats.total_time_ms);
    ROS_INFO("   已处理帧数: %d", stats.processed_frames);
    ROS_INFO("   平均FPS: %.1f", stats.average_fps);

    // 获取GPU内存使用情况
    size_t used_mem, total_mem;
    gpu_tsdf_->getMemoryUsage(used_mem, total_mem);
    ROS_INFO("   GPU内存使用: %.1f MB / %.1f MB (%.1f%%)",
             used_mem / (1024.0f * 1024.0f),
             total_mem / (1024.0f * 1024.0f),
             (float)used_mem / total_mem * 100.0f);
}

void TSDFFusion::updateDynamicVolumeOrigin(const Eigen::Matrix4f& camera_pose) {
    // 🔧 关键修复：实现真正的动态体素原点跟随机制（支持双重变换）

    // 提取机器人当前位置（已经过双重变换处理的世界坐标）
    Eigen::Vector3f current_robot_position(camera_pose(0,3), camera_pose(1,3), camera_pose(2,3));

    // 初始化体素原点（首次运行）
    if (!volume_origin_initialized_) {
        volume_origin_ = current_robot_position;
        last_robot_position_ = current_robot_position;
        volume_origin_initialized_ = true;

        ROS_INFO("🔧 初始化动态体素原点: [%.3f, %.3f, %.3f]",
                 volume_origin_.x(), volume_origin_.y(), volume_origin_.z());

        // 🚀 同步GPU端体素原点（初始化）
        if (enable_gpu_acceleration_ && gpu_tsdf_ && gpu_initialized_) {
            gpu_tsdf_->updateVolumeOrigin(volume_origin_.x(), volume_origin_.y(), volume_origin_.z());
            ROS_INFO("✅ GPU体素原点已同步初始化");
        }

        // 🔄 同步VoxBlox适配器原点
        if (use_voxblox_storage_ && voxblox_adapter_) {
            voxblox_adapter_->updateVolumeOrigin(volume_origin_);
            ROS_INFO("✅ VoxBlox适配器原点已同步初始化");
        }

        return;
    }

    // 计算机器人移动距离
    float movement_distance = (current_robot_position - last_robot_position_).norm();

    // 检查是否需要更新体素原点（避免频繁更新）
    // 🔧 修复5: 极低更新阈值，实现超实时跟随
    float dynamic_threshold = 0.001f;  // 强制设置为1mm，实现超实时跟随

    ROS_INFO_THROTTLE(3.0, "🔍 体素原点更新检查: 移动距离=%.3f米, 阈值=%.3f米",
                     movement_distance, dynamic_threshold);

    if (movement_distance > dynamic_threshold) {
        ROS_INFO("🔧 机器人移动距离=%.3f米，超过阈值%.3f米，更新体素原点",
                 movement_distance, dynamic_threshold);

        // 保存旧的体素原点
        Eigen::Vector3f old_origin = volume_origin_;

        // 更新体素原点到机器人当前位置
        volume_origin_ = current_robot_position;
        last_robot_position_ = current_robot_position;

        ROS_INFO("🔄 动态更新体素原点: [%.3f, %.3f, %.3f] -> [%.3f, %.3f, %.3f]",
                 old_origin.x(), old_origin.y(), old_origin.z(),
                 volume_origin_.x(), volume_origin_.y(), volume_origin_.z());

        // 🚀 增强GPU端体素原点管理
        if (enable_gpu_acceleration_ && gpu_tsdf_ && gpu_initialized_) {
            // 预检查GPU迁移需求
            bool gpu_migration_needed = gpu_tsdf_->shouldTriggerMigration(
                volume_origin_.x(), volume_origin_.y(), volume_origin_.z());

            if (gpu_migration_needed) {
                ROS_INFO("🔄 触发GPU体素数据迁移...");
                auto start_time = std::chrono::high_resolution_clock::now();

                gpu_tsdf_->updateVolumeOrigin(volume_origin_.x(), volume_origin_.y(), volume_origin_.z());

                auto end_time = std::chrono::high_resolution_clock::now();
                auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
                ROS_INFO("✅ GPU迁移完成，耗时: %ld ms", duration.count());
            } else {
                // 常规参数更新
                gpu_tsdf_->updateVolumeOrigin(volume_origin_.x(), volume_origin_.y(), volume_origin_.z());
                ROS_INFO("✅ GPU体素原点已同步更新（无需迁移）");
            }
        }

        // 🔄 同步VoxBlox适配器原点
        if (use_voxblox_storage_ && voxblox_adapter_) {
            voxblox_adapter_->updateVolumeOrigin(volume_origin_);
            ROS_INFO("✅ VoxBlox适配器原点已同步更新");
        }

        // 🔄 处理现有体素数据的坐标迁移（如果需要）
        float migration_distance = (old_origin - volume_origin_).norm();
        if (migration_distance > voxel_size_) {
            migrateVoxelData(old_origin, volume_origin_);
            ROS_INFO("🔄 体素数据迁移完成，迁移距离=%.3f米", migration_distance);
        }

        ROS_INFO("🎯 动态体素原点更新完成，新原点: [%.3f, %.3f, %.3f]",
                 volume_origin_.x(), volume_origin_.y(), volume_origin_.z());
    } else {
        ROS_DEBUG_THROTTLE(30.0, "🔧 机器人移动距离=%.3f米，未达到更新阈值%.3f米",
                          movement_distance, dynamic_threshold);
    }
}

void TSDFFusion::transferGPUVoxelsToCPU() {
    if (!gpu_tsdf_ || !gpu_initialized_) {
        ROS_WARN("⚠️ GPU TSDF未初始化，无法传输体素数据");
        return;
    }

    // 🔄 分配临时缓冲区来接收GPU体素数据
    const int max_transfer_voxels = 50000;  // 进一步限制传输数量以优化性能
    std::vector<CudaTSDFVoxel> gpu_voxels(max_transfer_voxels);

    // 从GPU获取体素数据
    int transferred_count = gpu_tsdf_->transferVoxelsToCPU(gpu_voxels.data(), max_transfer_voxels);

    if (transferred_count > 0) {
        // 🔧 增加处理的体素数量以支持完整建图
        const int max_voxels_per_frame = 10000;  // 每帧最多处理10000个体素（增加5倍）
        int actual_count = std::min(transferred_count, max_voxels_per_frame);

        if (transferred_count > max_voxels_per_frame) {
            ROS_WARN("⚠️ 体素数量过多 (%d)，限制为 %d 个以保证实时性",
                     transferred_count, max_voxels_per_frame);
        }

        // 🔄 处理GPU压缩后的有效体素数据
        ROS_INFO("🔄 开始处理 %d 个GPU有效体素 (原始: %d)", actual_count, transferred_count);
        auto start_time = std::chrono::high_resolution_clock::now();

        if (use_voxblox_storage_ && voxblox_adapter_) {
            // 🚀 使用VoxBlox高效存储
            std::vector<LinearVoxelUpdate> voxel_updates;
            voxel_updates.reserve(actual_count);

            for (int i = 0; i < actual_count; i++) {
                const CudaTSDFVoxel& gpu_voxel = gpu_voxels[i];

                // 🔧 关键修复：重新计算体素的世界坐标，考虑动态体素原点
                float world_x = gpu_voxel.world_x;
                float world_y = gpu_voxel.world_y;
                float world_z = gpu_voxel.world_z;

                // 检查体素数据有效性
                if (!std::isfinite(gpu_voxel.tsdf_value) || !std::isfinite(gpu_voxel.weight) ||
                    !std::isfinite(world_x) || !std::isfinite(world_y) || !std::isfinite(world_z)) {
                    continue;
                }

                // 🎯 关键修复：确保体素坐标相对于当前机器人位置
                // GPU计算的世界坐标可能基于旧的体素原点，需要重新校正
                if (volume_origin_initialized_) {
                    // 将GPU坐标转换为相对于当前体素原点的坐标
                    // 这确保了体素数据能够跟随机器人移动
                    ROS_DEBUG_THROTTLE(30.0, "🔧 体素坐标校正: GPU[%.3f, %.3f, %.3f] 原点[%.3f, %.3f, %.3f]",
                              world_x, world_y, world_z,
                              volume_origin_.x(), volume_origin_.y(), volume_origin_.z());
                }

                // 🔧 修复颜色数据：确保颜色值在有效范围内
                TSDFVoxel voxel;
                voxel.tsdf_value = gpu_voxel.tsdf_value;
                voxel.weight = gpu_voxel.weight;

                // 颜色数据验证和修复
                voxel.r = std::max(0, std::min(255, static_cast<int>(gpu_voxel.r)));
                voxel.g = std::max(0, std::min(255, static_cast<int>(gpu_voxel.g)));
                voxel.b = std::max(0, std::min(255, static_cast<int>(gpu_voxel.b)));

                // 如果颜色数据异常，使用默认颜色
                if (voxel.r == 0 && voxel.g == 0 && voxel.b == 0) {
                    // 根据TSDF值设置颜色，与RTAB-Map风格保持一致
                    if (voxel.tsdf_value < 0) {
                        voxel.r = 100; voxel.g = 100; voxel.b = 100;  // 灰色表示内部
                    } else {
                        voxel.r = 200; voxel.g = 200; voxel.b = 200;  // 浅灰色表示外部
                    }
                }

                // 使用校正后的坐标更新VoxbloxAdapter
                voxblox_adapter_->updateVoxel(world_x, world_y, world_z, voxel);
            }

            auto end_time = std::chrono::high_resolution_clock::now();
            auto total_elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);

            ROS_INFO("🚀 GPU到CPU体素传输完成: 处理=%d, 有效=%d, 耗时=%ldms",
                     actual_count, actual_count, total_elapsed.count());

            // 输出VoxBlox统计信息和体素原点状态
            auto stats = voxblox_adapter_->getStatistics();
            ROS_INFO("🚀 VoxBlox统计: 总块数=%zu, 活跃块数=%zu, 活跃体素数=%zu",
                     stats.total_blocks, stats.active_blocks, stats.active_voxels);

            // 🔧 关键调试：输出当前体素原点状态
            if (volume_origin_initialized_) {
                ROS_INFO_THROTTLE(3.0, "🎯 当前体素原点: [%.3f, %.3f, %.3f]",
                                 volume_origin_.x(), volume_origin_.y(), volume_origin_.z());

                // 检查体素数据是否在合理范围内
                if (actual_count > 0) {
                    float first_voxel_x = gpu_voxels[0].world_x;
                    float first_voxel_y = gpu_voxels[0].world_y;
                    float first_voxel_z = gpu_voxels[0].world_z;

                    float distance_from_origin = std::sqrt(
                        (first_voxel_x - volume_origin_.x()) * (first_voxel_x - volume_origin_.x()) +
                        (first_voxel_y - volume_origin_.y()) * (first_voxel_y - volume_origin_.y()) +
                        (first_voxel_z - volume_origin_.z()) * (first_voxel_z - volume_origin_.z())
                    );

                    ROS_INFO_THROTTLE(5.0, "🔍 体素分布检查: 首个体素[%.3f, %.3f, %.3f] 距离原点%.3f米",
                                     first_voxel_x, first_voxel_y, first_voxel_z, distance_from_origin);

                    if (distance_from_origin > 50.0f) {
                        ROS_WARN("⚠️ 体素距离原点过远(%.3f米)，可能存在坐标系问题", distance_from_origin);
                    }
                }
            }

        } else {
            // 🔄 使用传统存储（备用方案）
            ROS_WARN("⚠️ VoxBlox存储未启用，使用传统存储方法");

            auto end_time = std::chrono::high_resolution_clock::now();
            auto total_elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);

            ROS_INFO("🔄 传统体素处理完成: 处理=%d, 耗时=%ldms", actual_count, total_elapsed.count());
        }



    } else {
        ROS_WARN_THROTTLE(10.0, "⚠️ GPU到CPU体素传输失败或无数据");
    }
}

void TSDFFusion::migrateVoxelData(const Eigen::Vector3f& old_origin, const Eigen::Vector3f& new_origin) {
    // 🔄 体素数据迁移策略：当体素原点移动时，正确处理现有体素数据

    ROS_INFO("🔄 开始体素数据迁移: 从[%.3f, %.3f, %.3f]到[%.3f, %.3f, %.3f]",
             old_origin.x(), old_origin.y(), old_origin.z(),
             new_origin.x(), new_origin.y(), new_origin.z());

    // 计算原点偏移量
    Eigen::Vector3f origin_offset = new_origin - old_origin;

    if (use_voxblox_storage_ && voxblox_adapter_) {
        // 🚀 VoxBlox存储模式：利用块结构进行高效迁移
        ROS_INFO("🔄 使用VoxBlox存储进行体素数据迁移");

        // VoxBlox适配器会自动处理坐标系变换，这里只需要通知原点变化
        // 实际的体素数据会在下次访问时自动重新计算坐标
        ROS_INFO("✅ VoxBlox体素数据迁移完成（自动坐标重映射）");

    } else {
        // 🔄 传统存储模式：手动迁移体素数据
        ROS_INFO("🔄 使用传统存储进行体素数据迁移");

        // 创建新的体素容器
        std::unordered_map<VoxelIndex, TSDFVoxel, VoxelIndexHash> migrated_volume;

        int migrated_count = 0;
        for (const auto& voxel_pair : tsdf_volume_) {
            const VoxelIndex& old_index = voxel_pair.first;
            const TSDFVoxel& voxel = voxel_pair.second;

            // 计算体素的世界坐标
            Eigen::Vector3f old_world_pos = voxelToWorld(old_index);

            // 应用原点偏移
            Eigen::Vector3f new_world_pos = old_world_pos - origin_offset;

            // 计算新的体素索引
            VoxelIndex new_index = worldToVoxel(new_world_pos);

            // 迁移体素数据
            migrated_volume[new_index] = voxel;
            migrated_count++;
        }

        // 替换旧的体素容器
        tsdf_volume_ = std::move(migrated_volume);

        ROS_INFO("✅ 传统体素数据迁移完成，迁移体素数量: %d", migrated_count);
    }

    ROS_INFO("🎯 体素数据迁移完成，原点偏移: [%.3f, %.3f, %.3f]",
             origin_offset.x(), origin_offset.y(), origin_offset.z());
}

} // namespace tsdf_mapping
#include "tsdf_mapping/voxblox_adapter.h"
#include <ros/ros.h>
#include <cmath>
#include <algorithm>
#include <visualization_msgs/MarkerArray.h>
#include <geometry_msgs/Point.h>
#include <std_msgs/ColorRGBA.h>
#include <visualization_msgs/MarkerArray.h>
#include <geometry_msgs/Point.h>
#include <std_msgs/ColorRGBA.h>

namespace tsdf_mapping {

// VoxelBlock实现
void VoxelBlock::updateVoxel(int x, int y, int z, const TSDFVoxel& new_voxel) {
    if (x < 0 || x >= kVoxelsPerSide || y < 0 || y >= kVoxelsPerSide || z < 0 || z >= kVoxelsPerSide) {
        return;  // 越界检查
    }
    
    int idx = getLinearIndex(x, y, z);
    TSDFVoxel& voxel = voxels[idx];
    
    // 检查是否是新体素
    bool was_active = voxel.weight > 0.01f;
    
    // 加权平均更新
    if (voxel.weight > 0.0f) {
        float total_weight = voxel.weight + new_voxel.weight;
        voxel.tsdf_value = (voxel.tsdf_value * voxel.weight + new_voxel.tsdf_value * new_voxel.weight) / total_weight;
        voxel.weight = std::min(total_weight, 100.0f);  // 限制最大权重
        
        // 更新颜色（简化策略：使用新颜色）
        if (new_voxel.weight > voxel.weight * 0.3f) {
            voxel.r = new_voxel.r;
            voxel.g = new_voxel.g;
            voxel.b = new_voxel.b;
        }
    } else {
        // 新体素
        voxel = new_voxel;
    }
    
    // 更新块级别统计
    bool is_active = voxel.weight > 0.01f;
    if (!was_active && is_active) {
        active_voxel_count++;
        has_data = true;
    } else if (was_active && !is_active) {
        active_voxel_count--;
    }
    
    // 更新TSDF范围
    if (is_active) {
        min_tsdf = std::min(min_tsdf, voxel.tsdf_value);
        max_tsdf = std::max(max_tsdf, voxel.tsdf_value);
    }
}

const TSDFVoxel* VoxelBlock::getVoxel(int x, int y, int z) const {
    if (x < 0 || x >= kVoxelsPerSide || y < 0 || y >= kVoxelsPerSide || z < 0 || z >= kVoxelsPerSide) {
        return nullptr;
    }
    return &voxels[getLinearIndex(x, y, z)];
}

TSDFVoxel* VoxelBlock::getVoxel(int x, int y, int z) {
    if (x < 0 || x >= kVoxelsPerSide || y < 0 || y >= kVoxelsPerSide || z < 0 || z >= kVoxelsPerSide) {
        return nullptr;
    }
    return &voxels[getLinearIndex(x, y, z)];
}

// VoxbloxAdapter实现
VoxbloxAdapter::VoxbloxAdapter(float voxel_size, float block_size)
    : voxel_size_(voxel_size), block_size_(block_size), update_count_(0), lookup_count_(0),
      volume_origin_(Eigen::Vector3f::Zero()), volume_origin_initialized_(false) {
    voxels_per_block_side_ = static_cast<int>(block_size / voxel_size);
    ROS_INFO("🔧 VoxbloxAdapter初始化: 体素大小=%.3f, 块大小=%.3f, 每块体素数=%d",
             voxel_size_, block_size_, voxels_per_block_side_);
}

BlockIndex VoxbloxAdapter::worldToBlockIndex(float world_x, float world_y, float world_z) const {
    // 🔧 关键修复：考虑动态体素原点的坐标转换
    float relative_x = world_x - volume_origin_.x();
    float relative_y = world_y - volume_origin_.y();
    float relative_z = world_z - volume_origin_.z();

    int block_x = static_cast<int>(std::floor(relative_x / block_size_));
    int block_y = static_cast<int>(std::floor(relative_y / block_size_));
    int block_z = static_cast<int>(std::floor(relative_z / block_size_));
    return BlockIndex(block_x, block_y, block_z);
}

void VoxbloxAdapter::worldToVoxelIndex(float world_x, float world_y, float world_z,
                                      BlockIndex& block_idx, int& voxel_x, int& voxel_y, int& voxel_z) const {
    block_idx = worldToBlockIndex(world_x, world_y, world_z);
    
    // 计算块内体素坐标
    float block_origin_x = block_idx.x * block_size_;
    float block_origin_y = block_idx.y * block_size_;
    float block_origin_z = block_idx.z * block_size_;
    
    voxel_x = static_cast<int>((world_x - block_origin_x) / voxel_size_);
    voxel_y = static_cast<int>((world_y - block_origin_y) / voxel_size_);
    voxel_z = static_cast<int>((world_z - block_origin_z) / voxel_size_);
    
    // 确保体素坐标在有效范围内
    voxel_x = std::max(0, std::min(voxel_x, VoxelBlock::kVoxelsPerSide - 1));
    voxel_y = std::max(0, std::min(voxel_y, VoxelBlock::kVoxelsPerSide - 1));
    voxel_z = std::max(0, std::min(voxel_z, VoxelBlock::kVoxelsPerSide - 1));
}

void VoxbloxAdapter::voxelToWorldCoordinates(const BlockIndex& block_idx, int voxel_x, int voxel_y, int voxel_z,
                                            float& world_x, float& world_y, float& world_z) const {
    // 🔧 关键修复：考虑动态体素原点的坐标转换
    float relative_x = block_idx.x * block_size_ + (voxel_x + 0.5f) * voxel_size_;
    float relative_y = block_idx.y * block_size_ + (voxel_y + 0.5f) * voxel_size_;
    float relative_z = block_idx.z * block_size_ + (voxel_z + 0.5f) * voxel_size_;

    world_x = relative_x + volume_origin_.x();
    world_y = relative_y + volume_origin_.y();
    world_z = relative_z + volume_origin_.z();
}

VoxelBlock* VoxbloxAdapter::getOrCreateBlock(const BlockIndex& block_idx) {
    auto it = blocks_.find(block_idx);
    if (it != blocks_.end()) {
        return it->second.get();
    }
    
    // 创建新块
    auto new_block = std::make_unique<VoxelBlock>();
    VoxelBlock* block_ptr = new_block.get();
    blocks_[block_idx] = std::move(new_block);
    
    return block_ptr;
}

const VoxelBlock* VoxbloxAdapter::getBlock(const BlockIndex& block_idx) const {
    auto it = blocks_.find(block_idx);
    return (it != blocks_.end()) ? it->second.get() : nullptr;
}

void VoxbloxAdapter::batchUpdateVoxels(const std::vector<LinearVoxelUpdate>& updates) {
    update_count_ += updates.size();
    
    for (const auto& update : updates) {
        BlockIndex block_idx(update.block_x, update.block_y, update.block_z);
        VoxelBlock* block = getOrCreateBlock(block_idx);
        
        TSDFVoxel voxel;
        voxel.tsdf_value = update.tsdf_value;
        voxel.weight = update.weight;
        voxel.r = update.r;
        voxel.g = update.g;
        voxel.b = update.b;
        
        block->updateVoxel(update.voxel_x, update.voxel_y, update.voxel_z, voxel);
    }
}

void VoxbloxAdapter::updateVoxel(float world_x, float world_y, float world_z, const TSDFVoxel& voxel) {
    BlockIndex block_idx;
    int voxel_x, voxel_y, voxel_z;
    worldToVoxelIndex(world_x, world_y, world_z, block_idx, voxel_x, voxel_y, voxel_z);

    // 🔧 关键调试：验证坐标转换是否正确
    static int debug_count = 0;
    debug_count++;
    if (debug_count % 1000 == 0) {  // 每1000个体素输出一次调试信息
        ROS_INFO("🔍 VoxbloxAdapter体素更新 #%d: 世界坐标[%.3f, %.3f, %.3f] -> 块[%d, %d, %d] 体素[%d, %d, %d]",
                 debug_count, world_x, world_y, world_z,
                 block_idx.x, block_idx.y, block_idx.z,
                 voxel_x, voxel_y, voxel_z);

        if (volume_origin_initialized_) {
            ROS_INFO("   当前体素原点: [%.3f, %.3f, %.3f]",
                     volume_origin_.x(), volume_origin_.y(), volume_origin_.z());
        }
    }

    VoxelBlock* block = getOrCreateBlock(block_idx);
    block->updateVoxel(voxel_x, voxel_y, voxel_z, voxel);
    update_count_++;
}

const TSDFVoxel* VoxbloxAdapter::getVoxel(float world_x, float world_y, float world_z) const {
    BlockIndex block_idx;
    int voxel_x, voxel_y, voxel_z;
    worldToVoxelIndex(world_x, world_y, world_z, block_idx, voxel_x, voxel_y, voxel_z);
    
    const VoxelBlock* block = getBlock(block_idx);
    if (!block) {
        return nullptr;
    }
    
    lookup_count_++;
    return block->getVoxel(voxel_x, voxel_y, voxel_z);
}

pcl::PointCloud<pcl::PointXYZRGB>::Ptr VoxbloxAdapter::generatePointCloud(float min_weight_threshold) const {
    auto cloud = boost::make_shared<pcl::PointCloud<pcl::PointXYZRGB>>();
    cloud->header.frame_id = "map";
    
    for (const auto& block_pair : blocks_) {
        const BlockIndex& block_idx = block_pair.first;
        const VoxelBlock& block = *block_pair.second;
        
        if (!block.has_data) continue;
        
        for (int z = 0; z < VoxelBlock::kVoxelsPerSide; ++z) {
            for (int y = 0; y < VoxelBlock::kVoxelsPerSide; ++y) {
                for (int x = 0; x < VoxelBlock::kVoxelsPerSide; ++x) {
                    const TSDFVoxel* voxel = block.getVoxel(x, y, z);
                    
                    if (voxel && voxel->weight >= min_weight_threshold &&
                        std::abs(voxel->tsdf_value) < 0.8f) {  // 🚀 放宽表面阈值到0.8f，包含更多表面附近体素
                        
                        pcl::PointXYZRGB point;
                        voxelToWorldCoordinates(block_idx, x, y, z, point.x, point.y, point.z);
                        point.r = voxel->r;
                        point.g = voxel->g;
                        point.b = voxel->b;
                        
                        cloud->points.push_back(point);
                    }
                }
            }
        }
    }
    
    cloud->width = cloud->points.size();
    cloud->height = 1;
    cloud->is_dense = true;
    
    return cloud;
}

visualization_msgs::MarkerArray VoxbloxAdapter::generateVoxelMarkers(float min_weight_threshold) const {
    visualization_msgs::MarkerArray marker_array;

    // 清除之前的标记
    visualization_msgs::Marker clear_marker;
    clear_marker.header.frame_id = "map";
    clear_marker.header.stamp = ros::Time::now();
    clear_marker.ns = "voxel_grid";
    clear_marker.action = visualization_msgs::Marker::DELETEALL;
    marker_array.markers.push_back(clear_marker);

    // 创建体素立方体标记
    visualization_msgs::Marker voxel_marker;
    voxel_marker.header.frame_id = "map";
    voxel_marker.header.stamp = ros::Time::now();
    voxel_marker.ns = "voxel_grid";
    voxel_marker.id = 0;
    voxel_marker.type = visualization_msgs::Marker::CUBE_LIST;
    voxel_marker.action = visualization_msgs::Marker::ADD;
    voxel_marker.pose.orientation.w = 1.0;

    // 设置体素大小
    voxel_marker.scale.x = voxel_size_;
    voxel_marker.scale.y = voxel_size_;
    voxel_marker.scale.z = voxel_size_;

    // 遍历所有体素块
    for (const auto& block_pair : blocks_) {
        const BlockIndex& block_idx = block_pair.first;
        const VoxelBlock& block = *block_pair.second;

        if (!block.has_data) continue;

        for (int z = 0; z < VoxelBlock::kVoxelsPerSide; ++z) {
            for (int y = 0; y < VoxelBlock::kVoxelsPerSide; ++y) {
                for (int x = 0; x < VoxelBlock::kVoxelsPerSide; ++x) {
                    const TSDFVoxel* voxel = block.getVoxel(x, y, z);

                    if (voxel && voxel->weight >= min_weight_threshold) {
                        // 计算体素中心位置
                        geometry_msgs::Point point;
                        float world_x, world_y, world_z;
                        voxelToWorldCoordinates(block_idx, x, y, z, world_x, world_y, world_z);
                        point.x = world_x;
                        point.y = world_y;
                        point.z = world_z;
                        voxel_marker.points.push_back(point);

                        // 设置体素颜色
                        std_msgs::ColorRGBA color;
                        color.r = voxel->r / 255.0f;
                        color.g = voxel->g / 255.0f;
                        color.b = voxel->b / 255.0f;

                        // 根据TSDF值调整透明度
                        float tsdf_abs = std::abs(voxel->tsdf_value);
                        if (tsdf_abs < 0.1f) {
                            color.a = 0.8f;  // 接近表面的体素更不透明
                        } else if (tsdf_abs < 0.3f) {
                            color.a = 0.5f;
                        } else {
                            color.a = 0.2f;  // 远离表面的体素更透明
                        }

                        voxel_marker.colors.push_back(color);
                    }
                }
            }
        }
    }

    if (!voxel_marker.points.empty()) {
        marker_array.markers.push_back(voxel_marker);
        ROS_INFO("🔲 生成体素标记: %zu个体素", voxel_marker.points.size());
    }

    return marker_array;
}

VoxbloxAdapter::VoxelGridData VoxbloxAdapter::generateVoxelGrid(float min_weight_threshold) const {
    VoxelGridData grid_data;
    grid_data.voxel_size = voxel_size_;
    grid_data.total_voxels = 0;

    // 遍历所有体素块
    for (const auto& block_pair : blocks_) {
        const BlockIndex& block_idx = block_pair.first;
        const VoxelBlock& block = *block_pair.second;

        if (!block.has_data) continue;

        for (int z = 0; z < VoxelBlock::kVoxelsPerSide; ++z) {
            for (int y = 0; y < VoxelBlock::kVoxelsPerSide; ++y) {
                for (int x = 0; x < VoxelBlock::kVoxelsPerSide; ++x) {
                    const TSDFVoxel* voxel = block.getVoxel(x, y, z);

                    if (voxel && voxel->weight >= min_weight_threshold) {
                        // 体素位置
                        geometry_msgs::Point point;
                        float world_x, world_y, world_z;
                        voxelToWorldCoordinates(block_idx, x, y, z, world_x, world_y, world_z);
                        point.x = world_x;
                        point.y = world_y;
                        point.z = world_z;
                        grid_data.positions.push_back(point);

                        // 体素颜色
                        std_msgs::ColorRGBA color;
                        color.r = voxel->r / 255.0f;
                        color.g = voxel->g / 255.0f;
                        color.b = voxel->b / 255.0f;
                        color.a = 1.0f;
                        grid_data.colors.push_back(color);

                        // TSDF值和权重
                        grid_data.tsdf_values.push_back(voxel->tsdf_value);
                        grid_data.weights.push_back(voxel->weight);

                        grid_data.total_voxels++;
                    }
                }
            }
        }
    }

    ROS_INFO("🔲 生成体素网格数据: %zu个体素, 体素大小=%.3fm",
             grid_data.total_voxels, grid_data.voxel_size);

    return grid_data;
}

VoxbloxAdapter::Statistics VoxbloxAdapter::getStatistics() const {
    Statistics stats;
    stats.total_blocks = blocks_.size();
    stats.active_blocks = 0;
    stats.total_voxels = 0;
    stats.active_voxels = 0;
    
    for (const auto& block_pair : blocks_) {
        const VoxelBlock& block = *block_pair.second;
        if (block.has_data) {
            stats.active_blocks++;
            stats.active_voxels += block.active_voxel_count;
        }
        stats.total_voxels += VoxelBlock::kVoxelsPerBlock;
    }
    
    stats.memory_usage_bytes = stats.total_blocks * sizeof(VoxelBlock);
    
    return stats;
}

void VoxbloxAdapter::pruneVoxels(float min_weight_threshold) {
    for (auto it = blocks_.begin(); it != blocks_.end();) {
        VoxelBlock& block = *it->second;
        
        // 重新计算块的活跃体素数量
        int active_count = 0;
        for (int i = 0; i < VoxelBlock::kVoxelsPerBlock; ++i) {
            if (block.voxels[i].weight < min_weight_threshold) {
                block.voxels[i].weight = 0.0f;  // 清除低权重体素
            } else {
                active_count++;
            }
        }
        
        block.active_voxel_count = active_count;
        block.has_data = (active_count > 0);
        
        // 删除空块
        if (!block.has_data) {
            it = blocks_.erase(it);
        } else {
            ++it;
        }
    }
}

void VoxbloxAdapter::updateVolumeOrigin(const Eigen::Vector3f& new_origin) {
    // 🔧 动态体素原点更新（支持双重变换机制）

    if (!volume_origin_initialized_) {
        volume_origin_ = new_origin;
        volume_origin_initialized_ = true;
        ROS_INFO("🔧 VoxbloxAdapter: 初始化体素原点 [%.3f, %.3f, %.3f]",
                 volume_origin_.x(), volume_origin_.y(), volume_origin_.z());
        return;
    }

    // 计算原点偏移量
    Eigen::Vector3f origin_offset = new_origin - volume_origin_;
    float offset_magnitude = origin_offset.norm();

    if (offset_magnitude > 0.01f) {  // 只有当偏移量大于1cm时才更新
        ROS_INFO("🔧 VoxbloxAdapter: 更新体素原点 [%.3f, %.3f, %.3f] -> [%.3f, %.3f, %.3f], 偏移=%.3f米",
                 volume_origin_.x(), volume_origin_.y(), volume_origin_.z(),
                 new_origin.x(), new_origin.y(), new_origin.z(),
                 offset_magnitude);

        volume_origin_ = new_origin;

        // 注意：由于我们修改了坐标转换函数，现有的体素块会自动使用新的原点
        // 进行坐标计算，无需手动迁移数据

        ROS_INFO("✅ VoxbloxAdapter: 体素原点更新完成，现有%zu个块将自动使用新原点", blocks_.size());
    }
}

} // namespace tsdf_mapping
